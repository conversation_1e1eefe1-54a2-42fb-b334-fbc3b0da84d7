<?php
/* @noinspection ALL */
// @formatter:off
// phpcs:ignoreFile

namespace PHPSTORM_META {

   /**
    * PhpStorm Meta file, to provide autocomplete information for PhpStorm
    *
    * <AUTHOR> vd. <PERSON>l <<EMAIL>>
    * @see https://github.com/barryvdh/laravel-ide-helper
    */
    override(new \Illuminate\Contracts\Container\Container, map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::get(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::make(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Illuminate\Contracts\Container\Container::makeWith(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::get(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::make(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\App::makeWith(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\app(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\resolve(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));
    override(\Psr\Container\ContainerInterface::get(0), map([
        '' => '@',
            'App\Media\Image\OptimizableImage' => \App\Media\Image\ImageCompression::class,
            'Filament\Http\Responses\Auth\Contracts\EmailVerificationResponse' => \Filament\Http\Responses\Auth\EmailVerificationResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LoginResponse' => \Filament\Http\Responses\Auth\LoginResponse::class,
            'Filament\Http\Responses\Auth\Contracts\LogoutResponse' => \Filament\Http\Responses\Auth\LogoutResponse::class,
            'Filament\Http\Responses\Auth\Contracts\PasswordResetResponse' => \Filament\Http\Responses\Auth\PasswordResetResponse::class,
            'Filament\Http\Responses\Auth\Contracts\RegistrationResponse' => \Filament\Http\Responses\Auth\RegistrationResponse::class,
            'Filament\Support\Components\Contracts\ScopedComponentManager' => \Filament\Support\Components\ComponentManager::class,
            'Illuminate\Bus\BatchRepository' => \Illuminate\Bus\DatabaseBatchRepository::class,
            'Illuminate\Contracts\Auth\Access\Gate' => \Illuminate\Auth\Access\Gate::class,
            'Illuminate\Contracts\Auth\StatefulGuard' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'Illuminate\Contracts\Broadcasting\Broadcaster' => \Illuminate\Broadcasting\Broadcasters\NullBroadcaster::class,
            'Illuminate\Contracts\Console\Kernel' => \Illuminate\Foundation\Console\Kernel::class,
            'Illuminate\Contracts\Debug\ExceptionHandler' => \NunoMaduro\Collision\Adapters\Laravel\ExceptionHandler::class,
            'Illuminate\Contracts\Foundation\ExceptionRenderer' => \Spatie\LaravelIgnition\Renderers\IgnitionExceptionRenderer::class,
            'Illuminate\Contracts\Foundation\MaintenanceMode' => \Illuminate\Foundation\FileBasedMaintenanceMode::class,
            'Illuminate\Contracts\Http\Kernel' => \Illuminate\Foundation\Http\Kernel::class,
            'Illuminate\Contracts\Log\ContextLogProcessor' => \Illuminate\Log\Context\ContextLogProcessor::class,
            'Illuminate\Contracts\Pipeline\Hub' => \Illuminate\Pipeline\Hub::class,
            'Illuminate\Contracts\Queue\EntityResolver' => \Illuminate\Database\Eloquent\QueueEntityResolver::class,
            'Illuminate\Contracts\Routing\ResponseFactory' => \Illuminate\Routing\ResponseFactory::class,
            'Illuminate\Contracts\Validation\UncompromisedVerifier' => \Illuminate\Validation\NotPwnedVerifier::class,
            'Illuminate\Routing\Contracts\CallableDispatcher' => \Illuminate\Routing\CallableDispatcher::class,
            'Illuminate\Routing\Contracts\ControllerDispatcher' => \Illuminate\Routing\ControllerDispatcher::class,
            'Inertia\Ssr\Gateway' => \Inertia\Ssr\HttpGateway::class,
            'Laravel\Fortify\Contracts\CreatesNewUsers' => \App\Actions\Fortify\CreateNewUser::class,
            'Laravel\Fortify\Contracts\EmailVerificationNotificationSentResponse' => \Laravel\Fortify\Http\Responses\EmailVerificationNotificationSentResponse::class,
            'Laravel\Fortify\Contracts\FailedPasswordConfirmationResponse' => \Laravel\Fortify\Http\Responses\FailedPasswordConfirmationResponse::class,
            'Laravel\Fortify\Contracts\FailedTwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\FailedTwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\LockoutResponse' => \Laravel\Fortify\Http\Responses\LockoutResponse::class,
            'Laravel\Fortify\Contracts\PasswordConfirmedResponse' => \Laravel\Fortify\Http\Responses\PasswordConfirmedResponse::class,
            'Laravel\Fortify\Contracts\PasswordUpdateResponse' => \Laravel\Fortify\Http\Responses\PasswordUpdateResponse::class,
            'Laravel\Fortify\Contracts\ProfileInformationUpdatedResponse' => \Laravel\Fortify\Http\Responses\ProfileInformationUpdatedResponse::class,
            'Laravel\Fortify\Contracts\RecoveryCodesGeneratedResponse' => \Laravel\Fortify\Http\Responses\RecoveryCodesGeneratedResponse::class,
            'Laravel\Fortify\Contracts\RegisterResponse' => \Laravel\Fortify\Http\Responses\RegisterResponse::class,
            'Laravel\Fortify\Contracts\ResetsUserPasswords' => \App\Actions\Fortify\ResetUserPassword::class,
            'Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider' => \Laravel\Fortify\TwoFactorAuthenticationProvider::class,
            'Laravel\Fortify\Contracts\TwoFactorConfirmedResponse' => \Laravel\Fortify\Http\Responses\TwoFactorConfirmedResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorDisabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorDisabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorEnabledResponse' => \Laravel\Fortify\Http\Responses\TwoFactorEnabledResponse::class,
            'Laravel\Fortify\Contracts\TwoFactorLoginResponse' => \Laravel\Fortify\Http\Responses\TwoFactorLoginResponse::class,
            'Laravel\Fortify\Contracts\UpdatesUserPasswords' => \App\Actions\Fortify\UpdateUserPassword::class,
            'Laravel\Fortify\Contracts\UpdatesUserProfileInformation' => \App\Actions\Fortify\UpdateUserProfileInformation::class,
            'Laravel\Fortify\Contracts\VerifyEmailResponse' => \Laravel\Fortify\Http\Responses\VerifyEmailResponse::class,
            'Laravel\Horizon\Contracts\HorizonCommandQueue' => \Laravel\Horizon\RedisHorizonCommandQueue::class,
            'Laravel\Horizon\Contracts\JobRepository' => \Laravel\Horizon\Repositories\RedisJobRepository::class,
            'Laravel\Horizon\Contracts\MasterSupervisorRepository' => \Laravel\Horizon\Repositories\RedisMasterSupervisorRepository::class,
            'Laravel\Horizon\Contracts\MetricsRepository' => \Laravel\Horizon\Repositories\RedisMetricsRepository::class,
            'Laravel\Horizon\Contracts\ProcessRepository' => \Laravel\Horizon\Repositories\RedisProcessRepository::class,
            'Laravel\Horizon\Contracts\SupervisorRepository' => \Laravel\Horizon\Repositories\RedisSupervisorRepository::class,
            'Laravel\Horizon\Contracts\TagRepository' => \Laravel\Horizon\Repositories\RedisTagRepository::class,
            'Laravel\Horizon\Contracts\WorkloadRepository' => \Laravel\Horizon\Repositories\RedisWorkloadRepository::class,
            'Laravel\Pulse\Contracts\Ingest' => \Laravel\Pulse\Ingests\StorageIngest::class,
            'Laravel\Pulse\Contracts\ResolvesUsers' => \Laravel\Pulse\Users::class,
            'Laravel\Pulse\Contracts\Storage' => \Laravel\Pulse\Storage\DatabaseStorage::class,
            'Laravel\Reverb\Contracts\ApplicationProvider' => \Laravel\Reverb\ConfigApplicationProvider::class,
            'Laravel\Socialite\Contracts\Factory' => \Laravel\Socialite\SocialiteManager::class,
            'Laravel\Telescope\Contracts\ClearableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\EntriesRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'Laravel\Telescope\Contracts\PrunableRepository' => \Laravel\Telescope\Storage\DatabaseEntriesRepository::class,
            'PhpParser\PrettyPrinter' => \PhpParser\PrettyPrinter\Standard::class,
            'Psr\Http\Message\ResponseInterface' => \GuzzleHttp\Psr7\Response::class,
            'Psr\Http\Message\ServerRequestInterface' => \GuzzleHttp\Psr7\ServerRequest::class,
            'Spatie\Backup\Tasks\Cleanup\CleanupStrategy' => \Spatie\Backup\Tasks\Cleanup\Strategies\DefaultStrategy::class,
            'Spatie\ErrorSolutions\Contracts\SolutionProviderRepository' => \Spatie\ErrorSolutions\SolutionProviderRepository::class,
            'Spatie\Ignition\Contracts\ConfigManager' => \Spatie\Ignition\Config\FileConfigManager::class,
            'Symfony\Component\HtmlSanitizer\HtmlSanitizerInterface' => \Symfony\Component\HtmlSanitizer\HtmlSanitizer::class,
            'auth' => \Illuminate\Auth\AuthManager::class,
            'auth.driver' => \Lab404\Impersonate\Guard\SessionGuard::class,
            'auth.password' => \Illuminate\Auth\Passwords\PasswordBrokerManager::class,
            'auth.password.broker' => \Illuminate\Auth\Passwords\PasswordBroker::class,
            'backup-temporary-project' => \Spatie\TemporaryDirectory\TemporaryDirectory::class,
            'blade.compiler' => \Illuminate\View\Compilers\BladeCompiler::class,
            'cache' => \Illuminate\Cache\CacheManager::class,
            'cache.store' => \Illuminate\Cache\Repository::class,
            'command.debugbar.clear' => \Barryvdh\Debugbar\Console\ClearCommand::class,
            'command.tinker' => \Laravel\Tinker\Console\TinkerCommand::class,
            'composer' => \Illuminate\Support\Composer::class,
            'cookie' => \Illuminate\Cookie\CookieJar::class,
            'db' => \Illuminate\Database\DatabaseManager::class,
            'db.connection' => \Illuminate\Database\PostgresConnection::class,
            'db.factory' => \Illuminate\Database\Connectors\ConnectionFactory::class,
            'db.schema' => \Illuminate\Database\Schema\PostgresBuilder::class,
            'db.transactions' => \Illuminate\Database\DatabaseTransactionsManager::class,
            'encrypter' => \Illuminate\Encryption\Encrypter::class,
            'events' => \Illuminate\Events\Dispatcher::class,
            'filament' => \Filament\FilamentManager::class,
            'files' => \Illuminate\Filesystem\Filesystem::class,
            'filesystem' => \Illuminate\Filesystem\FilesystemManager::class,
            'filesystem.cloud' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'filesystem.disk' => \Illuminate\Filesystem\LocalFilesystemAdapter::class,
            'flare.logger' => \Monolog\Logger::class,
            'hash' => \Illuminate\Hashing\HashManager::class,
            'hash.driver' => \Illuminate\Hashing\BcryptHasher::class,
            'inertia.testing.view-finder' => \Illuminate\View\FileViewFinder::class,
            'log' => \Illuminate\Log\LogManager::class,
            'mail.manager' => \Illuminate\Mail\MailManager::class,
            'mailer' => \Illuminate\Mail\Mailer::class,
            'memcached.connector' => \Illuminate\Cache\MemcachedConnector::class,
            'migration.creator' => \Illuminate\Database\Migrations\MigrationCreator::class,
            'migration.repository' => \Illuminate\Database\Migrations\DatabaseMigrationRepository::class,
            'migrator' => \Illuminate\Database\Migrations\Migrator::class,
            'octane' => \Laravel\Octane\Octane::class,
            'pipeline' => \Illuminate\Pipeline\Pipeline::class,
            'queue' => \Illuminate\Queue\QueueManager::class,
            'queue.connection' => \Laravel\Horizon\RedisQueue::class,
            'queue.failer' => \Illuminate\Queue\Failed\DatabaseUuidFailedJobProvider::class,
            'queue.listener' => \Illuminate\Queue\Listener::class,
            'queue.worker' => \Illuminate\Queue\Worker::class,
            'redirect' => \Illuminate\Routing\Redirector::class,
            'redis' => \Illuminate\Redis\RedisManager::class,
            'redis.connection' => \Illuminate\Redis\Connections\PredisConnection::class,
            'router' => \Illuminate\Routing\Router::class,
            'session' => \Illuminate\Session\SessionManager::class,
            'session.store' => \Illuminate\Session\Store::class,
            'translation.loader' => \Illuminate\Translation\FileLoader::class,
            'translator' => \Illuminate\Translation\Translator::class,
            'url' => \Illuminate\Routing\UrlGenerator::class,
            'validation.presence' => \Illuminate\Validation\DatabasePresenceVerifier::class,
            'view' => \Illuminate\View\Factory::class,
            'view.engine.resolver' => \Illuminate\View\Engines\EngineResolver::class,
            'view.finder' => \Illuminate\View\FileViewFinder::class,
        ]));

    override(\auth()->user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Contracts\Auth\Guard::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Support\Facades\Auth::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\request()->user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Http\Request::user(), map([
        '' => \App\Models\User::class,
    ]));
    override(\Illuminate\Support\Facades\Request::user(), map([
        '' => \App\Models\User::class,
    ]));

    override(\config(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.in_container' => 'boolean',
            'audio.hls_enabled' => 'boolean',
            'audio.hls_bitrates.low' => 'string',
            'audio.hls_bitrates.medium' => 'string',
            'audio.hls_bitrates.high' => 'string',
            'audio.hls_segment_length' => 'integer',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'backup.backup.name' => 'string',
            'backup.backup.source.files.include' => 'array',
            'backup.backup.source.files.exclude' => 'array',
            'backup.backup.source.files.follow_links' => 'boolean',
            'backup.backup.source.files.ignore_unreadable_directories' => 'boolean',
            'backup.backup.source.files.relative_path' => 'NULL',
            'backup.backup.source.databases' => 'array',
            'backup.backup.database_dump_compressor' => 'NULL',
            'backup.backup.database_dump_file_timestamp_format' => 'NULL',
            'backup.backup.database_dump_file_extension' => 'string',
            'backup.backup.destination.compression_method' => 'integer',
            'backup.backup.destination.compression_level' => 'integer',
            'backup.backup.destination.filename_prefix' => 'string',
            'backup.backup.destination.disks' => 'array',
            'backup.backup.temporary_directory' => 'string',
            'backup.backup.password' => 'NULL',
            'backup.backup.encryption' => 'string',
            'backup.backup.tries' => 'integer',
            'backup.backup.retry_delay' => 'integer',
            'backup.notifications.notifications.App\Notifications\Backup\BackupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\UnhealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\BackupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\HealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifiable' => 'string',
            'backup.notifications.mail.to' => 'string',
            'backup.notifications.mail.from.address' => 'string',
            'backup.notifications.mail.from.name' => 'string',
            'backup.notifications.telegram.chat_id' => 'string',
            'backup.notifications.slack.webhook_url' => 'string',
            'backup.notifications.slack.channel' => 'NULL',
            'backup.notifications.slack.username' => 'NULL',
            'backup.notifications.slack.icon' => 'NULL',
            'backup.notifications.discord.webhook_url' => 'string',
            'backup.notifications.discord.username' => 'string',
            'backup.notifications.discord.avatar_url' => 'string',
            'backup.monitor_backups.0.name' => 'string',
            'backup.monitor_backups.0.disks' => 'array',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumAgeInDays' => 'integer',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumStorageInMegabytes' => 'integer',
            'backup.cleanup.strategy' => 'string',
            'backup.cleanup.default_strategy.keep_all_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_daily_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_weekly_backups_for_weeks' => 'integer',
            'backup.cleanup.default_strategy.keep_monthly_backups_for_months' => 'integer',
            'backup.cleanup.default_strategy.keep_yearly_backups_for_years' => 'integer',
            'backup.cleanup.default_strategy.delete_oldest_backups_when_using_more_megabytes_than' => 'integer',
            'backup.cleanup.tries' => 'integer',
            'backup.cleanup.retry_delay' => 'integer',
            'broadcasting.default' => 'NULL',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'string',
            'broadcasting.connections.reverb.secret' => 'string',
            'broadcasting.connections.reverb.app_id' => 'string',
            'broadcasting.connections.reverb.options.host' => 'string',
            'broadcasting.connections.reverb.options.port' => 'string',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'string',
            'broadcasting.connections.pusher.secret' => 'string',
            'broadcasting.connections.pusher.app_id' => 'string',
            'broadcasting.connections.pusher.options.cluster' => 'string',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'string',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'concurrency.default' => 'string',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.options.persistent' => 'boolean',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.options.prefix' => 'string',
            'embeddings.max_audio_length' => 'integer',
            'embeddings.audio_dimension' => 'integer',
            'embeddings.text_dimension' => 'integer',
            'filament.broadcasting' => 'array',
            'filament.default_filesystem_disk' => 'string',
            'filament.assets_path' => 'NULL',
            'filament.cache_path' => 'string',
            'filament.livewire_loading_delay' => 'string',
            'filament.system_route_prefix' => 'string',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'NULL',
            'filesystems.disks.s3.secret' => 'NULL',
            'filesystems.disks.s3.region' => 'NULL',
            'filesystems.disks.s3.bucket' => 'NULL',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.s3.report' => 'boolean',
            'filesystems.disks.tmp.driver' => 'string',
            'filesystems.disks.tmp.root' => 'string',
            'filesystems.links./home/<USER>/projects/pngwasi/smovee/public/storage' => 'string',
            'fortify.guard' => 'string',
            'fortify.middleware' => 'array',
            'fortify.auth_middleware' => 'string',
            'fortify.passwords' => 'string',
            'fortify.username' => 'string',
            'fortify.email' => 'string',
            'fortify.views' => 'boolean',
            'fortify.home' => 'string',
            'fortify.prefix' => 'string',
            'fortify.domain' => 'NULL',
            'fortify.lowercase_usernames' => 'boolean',
            'fortify.limiters.login' => 'string',
            'fortify.limiters.two-factor' => 'string',
            'fortify.paths.login' => 'NULL',
            'fortify.paths.logout' => 'NULL',
            'fortify.paths.password.request' => 'NULL',
            'fortify.paths.password.reset' => 'NULL',
            'fortify.paths.password.email' => 'NULL',
            'fortify.paths.password.update' => 'NULL',
            'fortify.paths.password.confirm' => 'NULL',
            'fortify.paths.password.confirmation' => 'NULL',
            'fortify.paths.register' => 'NULL',
            'fortify.paths.verification.notice' => 'NULL',
            'fortify.paths.verification.verify' => 'NULL',
            'fortify.paths.verification.send' => 'NULL',
            'fortify.paths.user-profile-information.update' => 'NULL',
            'fortify.paths.user-password.update' => 'NULL',
            'fortify.paths.two-factor.login' => 'NULL',
            'fortify.paths.two-factor.enable' => 'NULL',
            'fortify.paths.two-factor.confirm' => 'NULL',
            'fortify.paths.two-factor.disable' => 'NULL',
            'fortify.paths.two-factor.qr-code' => 'NULL',
            'fortify.paths.two-factor.secret-key' => 'NULL',
            'fortify.paths.two-factor.recovery-codes' => 'NULL',
            'fortify.redirects.login' => 'NULL',
            'fortify.redirects.logout' => 'NULL',
            'fortify.redirects.password-confirmation' => 'NULL',
            'fortify.redirects.register' => 'NULL',
            'fortify.redirects.email-verification' => 'NULL',
            'fortify.redirects.password-reset' => 'NULL',
            'fortify.features' => 'array',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'integer',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-default.connection' => 'string',
            'horizon.defaults.supervisor-default.queue' => 'array',
            'horizon.defaults.supervisor-default.balance' => 'string',
            'horizon.defaults.supervisor-default.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-default.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-default.maxTime' => 'integer',
            'horizon.defaults.supervisor-default.maxJobs' => 'integer',
            'horizon.defaults.supervisor-default.memory' => 'integer',
            'horizon.defaults.supervisor-default.tries' => 'integer',
            'horizon.defaults.supervisor-default.timeout' => 'integer',
            'horizon.defaults.supervisor-default.nice' => 'integer',
            'horizon.defaults.supervisor-recommender.connection' => 'string',
            'horizon.defaults.supervisor-recommender.queue' => 'array',
            'horizon.defaults.supervisor-recommender.balance' => 'string',
            'horizon.defaults.supervisor-recommender.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-recommender.maxTime' => 'integer',
            'horizon.defaults.supervisor-recommender.maxJobs' => 'integer',
            'horizon.defaults.supervisor-recommender.memory' => 'integer',
            'horizon.defaults.supervisor-recommender.tries' => 'integer',
            'horizon.defaults.supervisor-recommender.timeout' => 'integer',
            'horizon.defaults.supervisor-recommender.nice' => 'integer',
            'horizon.environments.production.supervisor-default.balance' => 'string',
            'horizon.environments.production.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-default.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-default.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-recommender.balance' => 'string',
            'horizon.environments.production.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-recommender.maxProcesses' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'laravel-impersonate.session_key' => 'string',
            'laravel-impersonate.session_guard' => 'string',
            'laravel-impersonate.session_guard_using' => 'string',
            'laravel-impersonate.default_impersonator_guard' => 'string',
            'laravel-impersonate.take_redirect_to' => 'string',
            'laravel-impersonate.leave_redirect_to' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'string',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'livewire.asset_url' => 'NULL',
            'livewire.app_url' => 'NULL',
            'livewire.middleware_group' => 'string',
            'livewire.manifest_path' => 'NULL',
            'livewire.back_button_cache' => 'boolean',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'integer',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'NULL',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.with.stream' => 'string',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.scheme' => 'NULL',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'metrics.access_restricted' => 'boolean',
            'metrics.allowed_ips' => 'array',
            'metrics.require_auth' => 'boolean',
            'metrics.require_admin' => 'boolean',
            'metrics.api_token' => 'NULL',
            'metrics.cache_duration' => 'integer',
            'metrics.enabled_metrics.application' => 'boolean',
            'metrics.enabled_metrics.business' => 'boolean',
            'metrics.enabled_metrics.infrastructure' => 'boolean',
            'metrics.enabled_metrics.performance' => 'boolean',
            'metrics.enabled_metrics.recommendations' => 'boolean',
            'metrics.enabled_metrics.audio' => 'boolean',
            'metrics.enabled_metrics.search' => 'boolean',
            'metrics.business.active_user_periods.daily' => 'integer',
            'metrics.business.active_user_periods.weekly' => 'integer',
            'metrics.business.active_user_periods.monthly' => 'integer',
            'metrics.business.new_content_periods.daily' => 'integer',
            'metrics.business.new_content_periods.weekly' => 'integer',
            'metrics.business.new_content_periods.monthly' => 'integer',
            'metrics.business.popular_track_min_plays' => 'integer',
            'metrics.business.popularity_window_days' => 'integer',
            'metrics.performance.enable_query_logging' => 'boolean',
            'metrics.performance.max_slow_queries' => 'integer',
            'metrics.performance.slow_query_threshold' => 'integer',
            'metrics.custom_metrics' => 'array',
            'metrics.thresholds.max_failed_jobs' => 'integer',
            'metrics.thresholds.max_queue_size' => 'integer',
            'metrics.thresholds.min_cache_hit_rate' => 'integer',
            'metrics.thresholds.max_response_time' => 'integer',
            'metrics.thresholds.min_disk_space_gb' => 'integer',
            'octane.server' => 'string',
            'octane.https' => 'boolean',
            'octane.listeners.Laravel\Octane\Events\WorkerStarting' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestHandled' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Contracts\OperationTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerErrorOccurred' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerStopping' => 'array',
            'octane.warm' => 'array',
            'octane.flush' => 'array',
            'octane.tables.example:1000.name' => 'string',
            'octane.tables.example:1000.votes' => 'string',
            'octane.cache.rows' => 'integer',
            'octane.cache.bytes' => 'integer',
            'octane.watch' => 'array',
            'octane.garbage' => 'integer',
            'octane.max_execution_time' => 'integer',
            'ollama-laravel.model' => 'string',
            'ollama-laravel.url' => 'string',
            'ollama-laravel.default_prompt' => 'string',
            'ollama-laravel.connection.timeout' => 'integer',
            'ollama-laravel.auth.type' => 'NULL',
            'ollama-laravel.auth.token' => 'NULL',
            'ollama-laravel.auth.username' => 'NULL',
            'ollama-laravel.auth.password' => 'NULL',
            'ollama-laravel.headers' => 'array',
            'pennant.default' => 'string',
            'pennant.stores.array.driver' => 'string',
            'pennant.stores.database.driver' => 'string',
            'pennant.stores.database.connection' => 'NULL',
            'pennant.stores.database.table' => 'string',
            'pulse.domain' => 'NULL',
            'pulse.path' => 'string',
            'pulse.enabled' => 'boolean',
            'pulse.storage.driver' => 'string',
            'pulse.storage.trim.keep' => 'string',
            'pulse.storage.database.connection' => 'NULL',
            'pulse.storage.database.chunk' => 'integer',
            'pulse.ingest.driver' => 'string',
            'pulse.ingest.buffer' => 'integer',
            'pulse.ingest.trim.lottery' => 'array',
            'pulse.ingest.trim.keep' => 'string',
            'pulse.ingest.redis.connection' => 'NULL',
            'pulse.ingest.redis.chunk' => 'integer',
            'pulse.cache' => 'NULL',
            'pulse.middleware' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.groups./^job-exceptions:.*/' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.server_name' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.directories' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.groups' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.max_query_length' => 'NULL',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.ignore' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.database.timeout' => 'integer',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.redis.timeout' => 'integer',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'recommendations.total_recommendation_items_limit' => 'integer',
            'recommendations.strategy_candidate_limit' => 'integer',
            'recommendations.recent_plays_days' => 'integer',
            'recommendations.popularity_days' => 'integer',
            'recommendations.following_days' => 'integer',
            'recommendations.max_seed_items_per_strategy' => 'integer',
            'recommendations.neighbors_per_seed' => 'integer',
            'recommendations.min_popular_plays' => 'integer',
            'recommendations.min_popular_plays_genre' => 'integer',
            'recommendations.target_playlist_count' => 'integer',
            'recommendations.items_per_playlist' => 'integer',
            'recommendations.playlist_freshness_days' => 'integer',
            'recommendations.max_user_top_genres' => 'integer',
            'recommendations.min_plays_for_top_genre' => 'integer',
            'recommendations.weights.following' => 'double',
            'recommendations.weights.recent_play_similarity' => 'double',
            'recommendations.weights.like_similarity' => 'double',
            'recommendations.weights.popular_genre' => 'double',
            'recommendations.weights.popular_global' => 'double',
            'recommendations.weights.fallback_popular' => 'double',
            'recommendations.weights.collaborative' => 'double',
            'recommendations.weights.top_listened' => 'double',
            'recommendations.diversity.max_artist_ratio' => 'double',
            'recommendations.diversity.min_genres' => 'integer',
            'recommendations.diversity.max_similarity' => 'double',
            'recommendations.diversity.max_embedding_iterations' => 'integer',
            'recommendations.collaborative.enabled' => 'boolean',
            'recommendations.collaborative.min_user_interactions' => 'integer',
            'recommendations.collaborative.max_similar_users' => 'integer',
            'recommendations.collaborative.min_similarity_score' => 'integer',
            'recommendations.collaborative.recommendation_limit' => 'integer',
            'recommendations.enabled_strategies' => 'array',
            'recommendations.recently_played_albums_lookback_days' => 'integer',
            'recommendations.recently_played_albums_limit' => 'integer',
            'recommendations.dj_mix_neighbors_to_fetch' => 'integer',
            'recommendations.dj_mix_exclude_recent_plays_days' => 'integer',
            'recommendations.dj_mix_use_embedding_average_count' => 'integer',
            'recommendations.dj_mix_similarity_vs_diversity_balance' => 'double',
            'recommendations.duplicate_detection.enabled' => 'boolean',
            'recommendations.duplicate_detection.name_similarity_threshold' => 'double',
            'recommendations.duplicate_detection.check_same_artist' => 'boolean',
            'recommendations.duplicate_detection.check_audio_duration' => 'boolean',
            'recommendations.duplicate_detection.duration_tolerance_seconds' => 'integer',
            'recommendations.duplicate_detection.check_audio_metadata' => 'boolean',
            'recommendations.duplicate_detection.tempo_tolerance_bpm' => 'integer',
            'recommendations.duplicate_detection.exclude_duplicate_artists_consecutive' => 'boolean',
            'recommendations.duplicate_detection.max_candidates_to_check' => 'integer',
            'recommendations.top_listened_playlist_enabled' => 'boolean',
            'recommendations.top_listened_playlist_size' => 'integer',
            'recommendations.top_listened_playlist_days' => 'integer',
            'recommendations.top_listened_playlist_min_items' => 'integer',
            'recommendations.top_listened_playlist_title' => 'string',
            'reverb.default' => 'string',
            'reverb.servers.reverb.host' => 'string',
            'reverb.servers.reverb.port' => 'integer',
            'reverb.servers.reverb.hostname' => 'string',
            'reverb.servers.reverb.options.tls' => 'array',
            'reverb.servers.reverb.max_request_size' => 'integer',
            'reverb.servers.reverb.scaling.enabled' => 'boolean',
            'reverb.servers.reverb.scaling.channel' => 'string',
            'reverb.servers.reverb.scaling.server.url' => 'NULL',
            'reverb.servers.reverb.scaling.server.host' => 'string',
            'reverb.servers.reverb.scaling.server.port' => 'string',
            'reverb.servers.reverb.scaling.server.username' => 'NULL',
            'reverb.servers.reverb.scaling.server.password' => 'NULL',
            'reverb.servers.reverb.scaling.server.database' => 'string',
            'reverb.servers.reverb.pulse_ingest_interval' => 'integer',
            'reverb.servers.reverb.telescope_ingest_interval' => 'integer',
            'reverb.apps.provider' => 'string',
            'reverb.apps.apps.0.key' => 'string',
            'reverb.apps.apps.0.secret' => 'string',
            'reverb.apps.apps.0.app_id' => 'string',
            'reverb.apps.apps.0.options.host' => 'string',
            'reverb.apps.apps.0.options.port' => 'string',
            'reverb.apps.apps.0.options.scheme' => 'string',
            'reverb.apps.apps.0.options.useTLS' => 'boolean',
            'reverb.apps.apps.0.allowed_origins' => 'array',
            'reverb.apps.apps.0.ping_interval' => 'integer',
            'reverb.apps.apps.0.activity_timeout' => 'integer',
            'reverb.apps.apps.0.max_message_size' => 'integer',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\User.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.sortableAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'scramble.api_path' => 'string',
            'scramble.api_domain' => 'NULL',
            'scramble.export_path' => 'string',
            'scramble.info.version' => 'string',
            'scramble.info.description' => 'string',
            'scramble.ui.title' => 'NULL',
            'scramble.ui.theme' => 'string',
            'scramble.ui.hide_try_it' => 'boolean',
            'scramble.ui.hide_schemas' => 'boolean',
            'scramble.ui.logo' => 'string',
            'scramble.ui.try_it_credentials_policy' => 'string',
            'scramble.servers' => 'NULL',
            'scramble.enum_cases_description_strategy' => 'string',
            'scramble.middleware' => 'array',
            'scramble.extensions' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.notifications.bot_user_oauth_token' => 'NULL',
            'services.slack.notifications.channel' => 'NULL',
            'services.google.client_id' => 'string',
            'services.google.client_secret' => 'string',
            'services.google.redirect' => 'string',
            'services.mailgun.domain' => 'NULL',
            'services.mailgun.secret' => 'NULL',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.telegram-bot-api.token' => 'string',
            'services.audio_metadata_extractor.url' => 'NULL',
            'services.audio_embedding_generator.url' => 'string',
            'services.text_embedder.url' => 'NULL',
            'services.image_generator.url' => 'NULL',
            'services.flutterwave.base_url' => 'string',
            'services.flutterwave.secret_key' => 'NULL',
            'services.flutterwave.public_key' => 'NULL',
            'services.flutterwave.encryption_key' => 'NULL',
            'services.flutterwave.is_live' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'NULL',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\BatchWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.hidden' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ClientRequestWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.always' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.level' => 'string',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.hydrations' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_http_methods' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_status_codes' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ViewWatcher' => 'boolean',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'ziggy.except' => 'array',
            'debugbar.enabled' => 'NULL',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'blade-heroicons.prefix' => 'string',
            'blade-heroicons.fallback' => 'string',
            'blade-heroicons.class' => 'string',
            'blade-heroicons.attributes' => 'array',
            'blade-icons.sets' => 'array',
            'blade-icons.class' => 'string',
            'blade-icons.attributes' => 'array',
            'blade-icons.fallback' => 'string',
            'blade-icons.components.disabled' => 'boolean',
            'blade-icons.components.default' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'flare.key' => 'NULL',
            'flare.flare_middleware' => 'array',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddLogs.maximum_number_of_collected_logs' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.maximum_number_of_collected_queries' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.report_query_bindings' => 'boolean',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddJobs.max_chained_job_reporting_depth' => 'integer',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestBodyFields.censor_fields' => 'array',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestHeaders.headers' => 'array',
            'flare.send_logs_as_events' => 'boolean',
            'ignition.editor' => 'string',
            'ignition.theme' => 'string',
            'ignition.enable_share_button' => 'boolean',
            'ignition.register_commands' => 'boolean',
            'ignition.solution_providers' => 'array',
            'ignition.ignored_solution_providers' => 'array',
            'ignition.enable_runnable_solutions' => 'NULL',
            'ignition.remote_sites_path' => 'string',
            'ignition.local_sites_path' => 'string',
            'ignition.housekeeping_endpoint_prefix' => 'string',
            'ignition.settings_file_path' => 'string',
            'ignition.recorders' => 'array',
            'ignition.open_ai_key' => 'NULL',
            'ignition.with_stack_frame_arguments' => 'boolean',
            'ignition.argument_reducers' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
        ]));
    override(\Illuminate\Config\Repository::get(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.in_container' => 'boolean',
            'audio.hls_enabled' => 'boolean',
            'audio.hls_bitrates.low' => 'string',
            'audio.hls_bitrates.medium' => 'string',
            'audio.hls_bitrates.high' => 'string',
            'audio.hls_segment_length' => 'integer',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'backup.backup.name' => 'string',
            'backup.backup.source.files.include' => 'array',
            'backup.backup.source.files.exclude' => 'array',
            'backup.backup.source.files.follow_links' => 'boolean',
            'backup.backup.source.files.ignore_unreadable_directories' => 'boolean',
            'backup.backup.source.files.relative_path' => 'NULL',
            'backup.backup.source.databases' => 'array',
            'backup.backup.database_dump_compressor' => 'NULL',
            'backup.backup.database_dump_file_timestamp_format' => 'NULL',
            'backup.backup.database_dump_file_extension' => 'string',
            'backup.backup.destination.compression_method' => 'integer',
            'backup.backup.destination.compression_level' => 'integer',
            'backup.backup.destination.filename_prefix' => 'string',
            'backup.backup.destination.disks' => 'array',
            'backup.backup.temporary_directory' => 'string',
            'backup.backup.password' => 'NULL',
            'backup.backup.encryption' => 'string',
            'backup.backup.tries' => 'integer',
            'backup.backup.retry_delay' => 'integer',
            'backup.notifications.notifications.App\Notifications\Backup\BackupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\UnhealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\BackupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\HealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifiable' => 'string',
            'backup.notifications.mail.to' => 'string',
            'backup.notifications.mail.from.address' => 'string',
            'backup.notifications.mail.from.name' => 'string',
            'backup.notifications.telegram.chat_id' => 'string',
            'backup.notifications.slack.webhook_url' => 'string',
            'backup.notifications.slack.channel' => 'NULL',
            'backup.notifications.slack.username' => 'NULL',
            'backup.notifications.slack.icon' => 'NULL',
            'backup.notifications.discord.webhook_url' => 'string',
            'backup.notifications.discord.username' => 'string',
            'backup.notifications.discord.avatar_url' => 'string',
            'backup.monitor_backups.0.name' => 'string',
            'backup.monitor_backups.0.disks' => 'array',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumAgeInDays' => 'integer',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumStorageInMegabytes' => 'integer',
            'backup.cleanup.strategy' => 'string',
            'backup.cleanup.default_strategy.keep_all_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_daily_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_weekly_backups_for_weeks' => 'integer',
            'backup.cleanup.default_strategy.keep_monthly_backups_for_months' => 'integer',
            'backup.cleanup.default_strategy.keep_yearly_backups_for_years' => 'integer',
            'backup.cleanup.default_strategy.delete_oldest_backups_when_using_more_megabytes_than' => 'integer',
            'backup.cleanup.tries' => 'integer',
            'backup.cleanup.retry_delay' => 'integer',
            'broadcasting.default' => 'NULL',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'string',
            'broadcasting.connections.reverb.secret' => 'string',
            'broadcasting.connections.reverb.app_id' => 'string',
            'broadcasting.connections.reverb.options.host' => 'string',
            'broadcasting.connections.reverb.options.port' => 'string',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'string',
            'broadcasting.connections.pusher.secret' => 'string',
            'broadcasting.connections.pusher.app_id' => 'string',
            'broadcasting.connections.pusher.options.cluster' => 'string',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'string',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'concurrency.default' => 'string',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.options.persistent' => 'boolean',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.options.prefix' => 'string',
            'embeddings.max_audio_length' => 'integer',
            'embeddings.audio_dimension' => 'integer',
            'embeddings.text_dimension' => 'integer',
            'filament.broadcasting' => 'array',
            'filament.default_filesystem_disk' => 'string',
            'filament.assets_path' => 'NULL',
            'filament.cache_path' => 'string',
            'filament.livewire_loading_delay' => 'string',
            'filament.system_route_prefix' => 'string',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'NULL',
            'filesystems.disks.s3.secret' => 'NULL',
            'filesystems.disks.s3.region' => 'NULL',
            'filesystems.disks.s3.bucket' => 'NULL',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.s3.report' => 'boolean',
            'filesystems.disks.tmp.driver' => 'string',
            'filesystems.disks.tmp.root' => 'string',
            'filesystems.links./home/<USER>/projects/pngwasi/smovee/public/storage' => 'string',
            'fortify.guard' => 'string',
            'fortify.middleware' => 'array',
            'fortify.auth_middleware' => 'string',
            'fortify.passwords' => 'string',
            'fortify.username' => 'string',
            'fortify.email' => 'string',
            'fortify.views' => 'boolean',
            'fortify.home' => 'string',
            'fortify.prefix' => 'string',
            'fortify.domain' => 'NULL',
            'fortify.lowercase_usernames' => 'boolean',
            'fortify.limiters.login' => 'string',
            'fortify.limiters.two-factor' => 'string',
            'fortify.paths.login' => 'NULL',
            'fortify.paths.logout' => 'NULL',
            'fortify.paths.password.request' => 'NULL',
            'fortify.paths.password.reset' => 'NULL',
            'fortify.paths.password.email' => 'NULL',
            'fortify.paths.password.update' => 'NULL',
            'fortify.paths.password.confirm' => 'NULL',
            'fortify.paths.password.confirmation' => 'NULL',
            'fortify.paths.register' => 'NULL',
            'fortify.paths.verification.notice' => 'NULL',
            'fortify.paths.verification.verify' => 'NULL',
            'fortify.paths.verification.send' => 'NULL',
            'fortify.paths.user-profile-information.update' => 'NULL',
            'fortify.paths.user-password.update' => 'NULL',
            'fortify.paths.two-factor.login' => 'NULL',
            'fortify.paths.two-factor.enable' => 'NULL',
            'fortify.paths.two-factor.confirm' => 'NULL',
            'fortify.paths.two-factor.disable' => 'NULL',
            'fortify.paths.two-factor.qr-code' => 'NULL',
            'fortify.paths.two-factor.secret-key' => 'NULL',
            'fortify.paths.two-factor.recovery-codes' => 'NULL',
            'fortify.redirects.login' => 'NULL',
            'fortify.redirects.logout' => 'NULL',
            'fortify.redirects.password-confirmation' => 'NULL',
            'fortify.redirects.register' => 'NULL',
            'fortify.redirects.email-verification' => 'NULL',
            'fortify.redirects.password-reset' => 'NULL',
            'fortify.features' => 'array',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'integer',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-default.connection' => 'string',
            'horizon.defaults.supervisor-default.queue' => 'array',
            'horizon.defaults.supervisor-default.balance' => 'string',
            'horizon.defaults.supervisor-default.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-default.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-default.maxTime' => 'integer',
            'horizon.defaults.supervisor-default.maxJobs' => 'integer',
            'horizon.defaults.supervisor-default.memory' => 'integer',
            'horizon.defaults.supervisor-default.tries' => 'integer',
            'horizon.defaults.supervisor-default.timeout' => 'integer',
            'horizon.defaults.supervisor-default.nice' => 'integer',
            'horizon.defaults.supervisor-recommender.connection' => 'string',
            'horizon.defaults.supervisor-recommender.queue' => 'array',
            'horizon.defaults.supervisor-recommender.balance' => 'string',
            'horizon.defaults.supervisor-recommender.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-recommender.maxTime' => 'integer',
            'horizon.defaults.supervisor-recommender.maxJobs' => 'integer',
            'horizon.defaults.supervisor-recommender.memory' => 'integer',
            'horizon.defaults.supervisor-recommender.tries' => 'integer',
            'horizon.defaults.supervisor-recommender.timeout' => 'integer',
            'horizon.defaults.supervisor-recommender.nice' => 'integer',
            'horizon.environments.production.supervisor-default.balance' => 'string',
            'horizon.environments.production.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-default.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-default.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-recommender.balance' => 'string',
            'horizon.environments.production.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-recommender.maxProcesses' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'laravel-impersonate.session_key' => 'string',
            'laravel-impersonate.session_guard' => 'string',
            'laravel-impersonate.session_guard_using' => 'string',
            'laravel-impersonate.default_impersonator_guard' => 'string',
            'laravel-impersonate.take_redirect_to' => 'string',
            'laravel-impersonate.leave_redirect_to' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'string',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'livewire.asset_url' => 'NULL',
            'livewire.app_url' => 'NULL',
            'livewire.middleware_group' => 'string',
            'livewire.manifest_path' => 'NULL',
            'livewire.back_button_cache' => 'boolean',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'integer',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'NULL',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.with.stream' => 'string',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.scheme' => 'NULL',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'metrics.access_restricted' => 'boolean',
            'metrics.allowed_ips' => 'array',
            'metrics.require_auth' => 'boolean',
            'metrics.require_admin' => 'boolean',
            'metrics.api_token' => 'NULL',
            'metrics.cache_duration' => 'integer',
            'metrics.enabled_metrics.application' => 'boolean',
            'metrics.enabled_metrics.business' => 'boolean',
            'metrics.enabled_metrics.infrastructure' => 'boolean',
            'metrics.enabled_metrics.performance' => 'boolean',
            'metrics.enabled_metrics.recommendations' => 'boolean',
            'metrics.enabled_metrics.audio' => 'boolean',
            'metrics.enabled_metrics.search' => 'boolean',
            'metrics.business.active_user_periods.daily' => 'integer',
            'metrics.business.active_user_periods.weekly' => 'integer',
            'metrics.business.active_user_periods.monthly' => 'integer',
            'metrics.business.new_content_periods.daily' => 'integer',
            'metrics.business.new_content_periods.weekly' => 'integer',
            'metrics.business.new_content_periods.monthly' => 'integer',
            'metrics.business.popular_track_min_plays' => 'integer',
            'metrics.business.popularity_window_days' => 'integer',
            'metrics.performance.enable_query_logging' => 'boolean',
            'metrics.performance.max_slow_queries' => 'integer',
            'metrics.performance.slow_query_threshold' => 'integer',
            'metrics.custom_metrics' => 'array',
            'metrics.thresholds.max_failed_jobs' => 'integer',
            'metrics.thresholds.max_queue_size' => 'integer',
            'metrics.thresholds.min_cache_hit_rate' => 'integer',
            'metrics.thresholds.max_response_time' => 'integer',
            'metrics.thresholds.min_disk_space_gb' => 'integer',
            'octane.server' => 'string',
            'octane.https' => 'boolean',
            'octane.listeners.Laravel\Octane\Events\WorkerStarting' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestHandled' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Contracts\OperationTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerErrorOccurred' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerStopping' => 'array',
            'octane.warm' => 'array',
            'octane.flush' => 'array',
            'octane.tables.example:1000.name' => 'string',
            'octane.tables.example:1000.votes' => 'string',
            'octane.cache.rows' => 'integer',
            'octane.cache.bytes' => 'integer',
            'octane.watch' => 'array',
            'octane.garbage' => 'integer',
            'octane.max_execution_time' => 'integer',
            'ollama-laravel.model' => 'string',
            'ollama-laravel.url' => 'string',
            'ollama-laravel.default_prompt' => 'string',
            'ollama-laravel.connection.timeout' => 'integer',
            'ollama-laravel.auth.type' => 'NULL',
            'ollama-laravel.auth.token' => 'NULL',
            'ollama-laravel.auth.username' => 'NULL',
            'ollama-laravel.auth.password' => 'NULL',
            'ollama-laravel.headers' => 'array',
            'pennant.default' => 'string',
            'pennant.stores.array.driver' => 'string',
            'pennant.stores.database.driver' => 'string',
            'pennant.stores.database.connection' => 'NULL',
            'pennant.stores.database.table' => 'string',
            'pulse.domain' => 'NULL',
            'pulse.path' => 'string',
            'pulse.enabled' => 'boolean',
            'pulse.storage.driver' => 'string',
            'pulse.storage.trim.keep' => 'string',
            'pulse.storage.database.connection' => 'NULL',
            'pulse.storage.database.chunk' => 'integer',
            'pulse.ingest.driver' => 'string',
            'pulse.ingest.buffer' => 'integer',
            'pulse.ingest.trim.lottery' => 'array',
            'pulse.ingest.trim.keep' => 'string',
            'pulse.ingest.redis.connection' => 'NULL',
            'pulse.ingest.redis.chunk' => 'integer',
            'pulse.cache' => 'NULL',
            'pulse.middleware' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.groups./^job-exceptions:.*/' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.server_name' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.directories' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.groups' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.max_query_length' => 'NULL',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.ignore' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.database.timeout' => 'integer',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.redis.timeout' => 'integer',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'recommendations.total_recommendation_items_limit' => 'integer',
            'recommendations.strategy_candidate_limit' => 'integer',
            'recommendations.recent_plays_days' => 'integer',
            'recommendations.popularity_days' => 'integer',
            'recommendations.following_days' => 'integer',
            'recommendations.max_seed_items_per_strategy' => 'integer',
            'recommendations.neighbors_per_seed' => 'integer',
            'recommendations.min_popular_plays' => 'integer',
            'recommendations.min_popular_plays_genre' => 'integer',
            'recommendations.target_playlist_count' => 'integer',
            'recommendations.items_per_playlist' => 'integer',
            'recommendations.playlist_freshness_days' => 'integer',
            'recommendations.max_user_top_genres' => 'integer',
            'recommendations.min_plays_for_top_genre' => 'integer',
            'recommendations.weights.following' => 'double',
            'recommendations.weights.recent_play_similarity' => 'double',
            'recommendations.weights.like_similarity' => 'double',
            'recommendations.weights.popular_genre' => 'double',
            'recommendations.weights.popular_global' => 'double',
            'recommendations.weights.fallback_popular' => 'double',
            'recommendations.weights.collaborative' => 'double',
            'recommendations.weights.top_listened' => 'double',
            'recommendations.diversity.max_artist_ratio' => 'double',
            'recommendations.diversity.min_genres' => 'integer',
            'recommendations.diversity.max_similarity' => 'double',
            'recommendations.diversity.max_embedding_iterations' => 'integer',
            'recommendations.collaborative.enabled' => 'boolean',
            'recommendations.collaborative.min_user_interactions' => 'integer',
            'recommendations.collaborative.max_similar_users' => 'integer',
            'recommendations.collaborative.min_similarity_score' => 'integer',
            'recommendations.collaborative.recommendation_limit' => 'integer',
            'recommendations.enabled_strategies' => 'array',
            'recommendations.recently_played_albums_lookback_days' => 'integer',
            'recommendations.recently_played_albums_limit' => 'integer',
            'recommendations.dj_mix_neighbors_to_fetch' => 'integer',
            'recommendations.dj_mix_exclude_recent_plays_days' => 'integer',
            'recommendations.dj_mix_use_embedding_average_count' => 'integer',
            'recommendations.dj_mix_similarity_vs_diversity_balance' => 'double',
            'recommendations.duplicate_detection.enabled' => 'boolean',
            'recommendations.duplicate_detection.name_similarity_threshold' => 'double',
            'recommendations.duplicate_detection.check_same_artist' => 'boolean',
            'recommendations.duplicate_detection.check_audio_duration' => 'boolean',
            'recommendations.duplicate_detection.duration_tolerance_seconds' => 'integer',
            'recommendations.duplicate_detection.check_audio_metadata' => 'boolean',
            'recommendations.duplicate_detection.tempo_tolerance_bpm' => 'integer',
            'recommendations.duplicate_detection.exclude_duplicate_artists_consecutive' => 'boolean',
            'recommendations.duplicate_detection.max_candidates_to_check' => 'integer',
            'recommendations.top_listened_playlist_enabled' => 'boolean',
            'recommendations.top_listened_playlist_size' => 'integer',
            'recommendations.top_listened_playlist_days' => 'integer',
            'recommendations.top_listened_playlist_min_items' => 'integer',
            'recommendations.top_listened_playlist_title' => 'string',
            'reverb.default' => 'string',
            'reverb.servers.reverb.host' => 'string',
            'reverb.servers.reverb.port' => 'integer',
            'reverb.servers.reverb.hostname' => 'string',
            'reverb.servers.reverb.options.tls' => 'array',
            'reverb.servers.reverb.max_request_size' => 'integer',
            'reverb.servers.reverb.scaling.enabled' => 'boolean',
            'reverb.servers.reverb.scaling.channel' => 'string',
            'reverb.servers.reverb.scaling.server.url' => 'NULL',
            'reverb.servers.reverb.scaling.server.host' => 'string',
            'reverb.servers.reverb.scaling.server.port' => 'string',
            'reverb.servers.reverb.scaling.server.username' => 'NULL',
            'reverb.servers.reverb.scaling.server.password' => 'NULL',
            'reverb.servers.reverb.scaling.server.database' => 'string',
            'reverb.servers.reverb.pulse_ingest_interval' => 'integer',
            'reverb.servers.reverb.telescope_ingest_interval' => 'integer',
            'reverb.apps.provider' => 'string',
            'reverb.apps.apps.0.key' => 'string',
            'reverb.apps.apps.0.secret' => 'string',
            'reverb.apps.apps.0.app_id' => 'string',
            'reverb.apps.apps.0.options.host' => 'string',
            'reverb.apps.apps.0.options.port' => 'string',
            'reverb.apps.apps.0.options.scheme' => 'string',
            'reverb.apps.apps.0.options.useTLS' => 'boolean',
            'reverb.apps.apps.0.allowed_origins' => 'array',
            'reverb.apps.apps.0.ping_interval' => 'integer',
            'reverb.apps.apps.0.activity_timeout' => 'integer',
            'reverb.apps.apps.0.max_message_size' => 'integer',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\User.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.sortableAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'scramble.api_path' => 'string',
            'scramble.api_domain' => 'NULL',
            'scramble.export_path' => 'string',
            'scramble.info.version' => 'string',
            'scramble.info.description' => 'string',
            'scramble.ui.title' => 'NULL',
            'scramble.ui.theme' => 'string',
            'scramble.ui.hide_try_it' => 'boolean',
            'scramble.ui.hide_schemas' => 'boolean',
            'scramble.ui.logo' => 'string',
            'scramble.ui.try_it_credentials_policy' => 'string',
            'scramble.servers' => 'NULL',
            'scramble.enum_cases_description_strategy' => 'string',
            'scramble.middleware' => 'array',
            'scramble.extensions' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.notifications.bot_user_oauth_token' => 'NULL',
            'services.slack.notifications.channel' => 'NULL',
            'services.google.client_id' => 'string',
            'services.google.client_secret' => 'string',
            'services.google.redirect' => 'string',
            'services.mailgun.domain' => 'NULL',
            'services.mailgun.secret' => 'NULL',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.telegram-bot-api.token' => 'string',
            'services.audio_metadata_extractor.url' => 'NULL',
            'services.audio_embedding_generator.url' => 'string',
            'services.text_embedder.url' => 'NULL',
            'services.image_generator.url' => 'NULL',
            'services.flutterwave.base_url' => 'string',
            'services.flutterwave.secret_key' => 'NULL',
            'services.flutterwave.public_key' => 'NULL',
            'services.flutterwave.encryption_key' => 'NULL',
            'services.flutterwave.is_live' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'NULL',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\BatchWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.hidden' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ClientRequestWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.always' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.level' => 'string',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.hydrations' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_http_methods' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_status_codes' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ViewWatcher' => 'boolean',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'ziggy.except' => 'array',
            'debugbar.enabled' => 'NULL',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'blade-heroicons.prefix' => 'string',
            'blade-heroicons.fallback' => 'string',
            'blade-heroicons.class' => 'string',
            'blade-heroicons.attributes' => 'array',
            'blade-icons.sets' => 'array',
            'blade-icons.class' => 'string',
            'blade-icons.attributes' => 'array',
            'blade-icons.fallback' => 'string',
            'blade-icons.components.disabled' => 'boolean',
            'blade-icons.components.default' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'flare.key' => 'NULL',
            'flare.flare_middleware' => 'array',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddLogs.maximum_number_of_collected_logs' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.maximum_number_of_collected_queries' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.report_query_bindings' => 'boolean',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddJobs.max_chained_job_reporting_depth' => 'integer',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestBodyFields.censor_fields' => 'array',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestHeaders.headers' => 'array',
            'flare.send_logs_as_events' => 'boolean',
            'ignition.editor' => 'string',
            'ignition.theme' => 'string',
            'ignition.enable_share_button' => 'boolean',
            'ignition.register_commands' => 'boolean',
            'ignition.solution_providers' => 'array',
            'ignition.ignored_solution_providers' => 'array',
            'ignition.enable_runnable_solutions' => 'NULL',
            'ignition.remote_sites_path' => 'string',
            'ignition.local_sites_path' => 'string',
            'ignition.housekeeping_endpoint_prefix' => 'string',
            'ignition.settings_file_path' => 'string',
            'ignition.recorders' => 'array',
            'ignition.open_ai_key' => 'NULL',
            'ignition.with_stack_frame_arguments' => 'boolean',
            'ignition.argument_reducers' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
        ]));
    override(\Illuminate\Support\Facades\Config::get(), map([
            'app.name' => 'string',
            'app.env' => 'string',
            'app.debug' => 'boolean',
            'app.url' => 'string',
            'app.frontend_url' => 'string',
            'app.asset_url' => 'NULL',
            'app.timezone' => 'string',
            'app.locale' => 'string',
            'app.fallback_locale' => 'string',
            'app.faker_locale' => 'string',
            'app.cipher' => 'string',
            'app.key' => 'string',
            'app.previous_keys' => 'array',
            'app.maintenance.driver' => 'string',
            'app.maintenance.store' => 'string',
            'app.providers' => 'array',
            'app.aliases.App' => 'string',
            'app.aliases.Arr' => 'string',
            'app.aliases.Artisan' => 'string',
            'app.aliases.Auth' => 'string',
            'app.aliases.Blade' => 'string',
            'app.aliases.Broadcast' => 'string',
            'app.aliases.Bus' => 'string',
            'app.aliases.Cache' => 'string',
            'app.aliases.Concurrency' => 'string',
            'app.aliases.Config' => 'string',
            'app.aliases.Context' => 'string',
            'app.aliases.Cookie' => 'string',
            'app.aliases.Crypt' => 'string',
            'app.aliases.Date' => 'string',
            'app.aliases.DB' => 'string',
            'app.aliases.Eloquent' => 'string',
            'app.aliases.Event' => 'string',
            'app.aliases.File' => 'string',
            'app.aliases.Gate' => 'string',
            'app.aliases.Hash' => 'string',
            'app.aliases.Http' => 'string',
            'app.aliases.Js' => 'string',
            'app.aliases.Lang' => 'string',
            'app.aliases.Log' => 'string',
            'app.aliases.Mail' => 'string',
            'app.aliases.Notification' => 'string',
            'app.aliases.Number' => 'string',
            'app.aliases.Password' => 'string',
            'app.aliases.Process' => 'string',
            'app.aliases.Queue' => 'string',
            'app.aliases.RateLimiter' => 'string',
            'app.aliases.Redirect' => 'string',
            'app.aliases.Request' => 'string',
            'app.aliases.Response' => 'string',
            'app.aliases.Route' => 'string',
            'app.aliases.Schedule' => 'string',
            'app.aliases.Schema' => 'string',
            'app.aliases.Session' => 'string',
            'app.aliases.Storage' => 'string',
            'app.aliases.Str' => 'string',
            'app.aliases.URL' => 'string',
            'app.aliases.Uri' => 'string',
            'app.aliases.Validator' => 'string',
            'app.aliases.View' => 'string',
            'app.aliases.Vite' => 'string',
            'app.in_container' => 'boolean',
            'audio.hls_enabled' => 'boolean',
            'audio.hls_bitrates.low' => 'string',
            'audio.hls_bitrates.medium' => 'string',
            'audio.hls_bitrates.high' => 'string',
            'audio.hls_segment_length' => 'integer',
            'auth.defaults.guard' => 'string',
            'auth.defaults.passwords' => 'string',
            'auth.guards.web.driver' => 'string',
            'auth.guards.web.provider' => 'string',
            'auth.guards.sanctum.driver' => 'string',
            'auth.guards.sanctum.provider' => 'NULL',
            'auth.providers.users.driver' => 'string',
            'auth.providers.users.model' => 'string',
            'auth.passwords.users.provider' => 'string',
            'auth.passwords.users.table' => 'string',
            'auth.passwords.users.expire' => 'integer',
            'auth.passwords.users.throttle' => 'integer',
            'auth.password_timeout' => 'integer',
            'backup.backup.name' => 'string',
            'backup.backup.source.files.include' => 'array',
            'backup.backup.source.files.exclude' => 'array',
            'backup.backup.source.files.follow_links' => 'boolean',
            'backup.backup.source.files.ignore_unreadable_directories' => 'boolean',
            'backup.backup.source.files.relative_path' => 'NULL',
            'backup.backup.source.databases' => 'array',
            'backup.backup.database_dump_compressor' => 'NULL',
            'backup.backup.database_dump_file_timestamp_format' => 'NULL',
            'backup.backup.database_dump_file_extension' => 'string',
            'backup.backup.destination.compression_method' => 'integer',
            'backup.backup.destination.compression_level' => 'integer',
            'backup.backup.destination.filename_prefix' => 'string',
            'backup.backup.destination.disks' => 'array',
            'backup.backup.temporary_directory' => 'string',
            'backup.backup.password' => 'NULL',
            'backup.backup.encryption' => 'string',
            'backup.backup.tries' => 'integer',
            'backup.backup.retry_delay' => 'integer',
            'backup.notifications.notifications.App\Notifications\Backup\BackupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\UnhealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupHasFailedNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\BackupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\HealthyBackupWasFoundNotification' => 'array',
            'backup.notifications.notifications.App\Notifications\Backup\CleanupWasSuccessfulNotification' => 'array',
            'backup.notifications.notifiable' => 'string',
            'backup.notifications.mail.to' => 'string',
            'backup.notifications.mail.from.address' => 'string',
            'backup.notifications.mail.from.name' => 'string',
            'backup.notifications.telegram.chat_id' => 'string',
            'backup.notifications.slack.webhook_url' => 'string',
            'backup.notifications.slack.channel' => 'NULL',
            'backup.notifications.slack.username' => 'NULL',
            'backup.notifications.slack.icon' => 'NULL',
            'backup.notifications.discord.webhook_url' => 'string',
            'backup.notifications.discord.username' => 'string',
            'backup.notifications.discord.avatar_url' => 'string',
            'backup.monitor_backups.0.name' => 'string',
            'backup.monitor_backups.0.disks' => 'array',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumAgeInDays' => 'integer',
            'backup.monitor_backups.0.health_checks.Spatie\Backup\Tasks\Monitor\HealthChecks\MaximumStorageInMegabytes' => 'integer',
            'backup.cleanup.strategy' => 'string',
            'backup.cleanup.default_strategy.keep_all_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_daily_backups_for_days' => 'integer',
            'backup.cleanup.default_strategy.keep_weekly_backups_for_weeks' => 'integer',
            'backup.cleanup.default_strategy.keep_monthly_backups_for_months' => 'integer',
            'backup.cleanup.default_strategy.keep_yearly_backups_for_years' => 'integer',
            'backup.cleanup.default_strategy.delete_oldest_backups_when_using_more_megabytes_than' => 'integer',
            'backup.cleanup.tries' => 'integer',
            'backup.cleanup.retry_delay' => 'integer',
            'broadcasting.default' => 'NULL',
            'broadcasting.connections.reverb.driver' => 'string',
            'broadcasting.connections.reverb.key' => 'string',
            'broadcasting.connections.reverb.secret' => 'string',
            'broadcasting.connections.reverb.app_id' => 'string',
            'broadcasting.connections.reverb.options.host' => 'string',
            'broadcasting.connections.reverb.options.port' => 'string',
            'broadcasting.connections.reverb.options.scheme' => 'string',
            'broadcasting.connections.reverb.options.useTLS' => 'boolean',
            'broadcasting.connections.reverb.client_options' => 'array',
            'broadcasting.connections.pusher.driver' => 'string',
            'broadcasting.connections.pusher.key' => 'string',
            'broadcasting.connections.pusher.secret' => 'string',
            'broadcasting.connections.pusher.app_id' => 'string',
            'broadcasting.connections.pusher.options.cluster' => 'string',
            'broadcasting.connections.pusher.options.host' => 'string',
            'broadcasting.connections.pusher.options.port' => 'string',
            'broadcasting.connections.pusher.options.scheme' => 'string',
            'broadcasting.connections.pusher.options.encrypted' => 'boolean',
            'broadcasting.connections.pusher.options.useTLS' => 'boolean',
            'broadcasting.connections.pusher.client_options' => 'array',
            'broadcasting.connections.ably.driver' => 'string',
            'broadcasting.connections.ably.key' => 'NULL',
            'broadcasting.connections.log.driver' => 'string',
            'broadcasting.connections.null.driver' => 'string',
            'cache.default' => 'string',
            'cache.stores.array.driver' => 'string',
            'cache.stores.array.serialize' => 'boolean',
            'cache.stores.database.driver' => 'string',
            'cache.stores.database.connection' => 'NULL',
            'cache.stores.database.table' => 'string',
            'cache.stores.database.lock_connection' => 'NULL',
            'cache.stores.database.lock_table' => 'NULL',
            'cache.stores.file.driver' => 'string',
            'cache.stores.file.path' => 'string',
            'cache.stores.file.lock_path' => 'string',
            'cache.stores.memcached.driver' => 'string',
            'cache.stores.memcached.persistent_id' => 'NULL',
            'cache.stores.memcached.sasl' => 'array',
            'cache.stores.memcached.options' => 'array',
            'cache.stores.memcached.servers.0.host' => 'string',
            'cache.stores.memcached.servers.0.port' => 'integer',
            'cache.stores.memcached.servers.0.weight' => 'integer',
            'cache.stores.redis.driver' => 'string',
            'cache.stores.redis.connection' => 'string',
            'cache.stores.redis.lock_connection' => 'string',
            'cache.stores.dynamodb.driver' => 'string',
            'cache.stores.dynamodb.key' => 'NULL',
            'cache.stores.dynamodb.secret' => 'NULL',
            'cache.stores.dynamodb.region' => 'string',
            'cache.stores.dynamodb.table' => 'string',
            'cache.stores.dynamodb.endpoint' => 'NULL',
            'cache.stores.octane.driver' => 'string',
            'cache.prefix' => 'string',
            'concurrency.default' => 'string',
            'cors.paths' => 'array',
            'cors.allowed_methods' => 'array',
            'cors.allowed_origins' => 'array',
            'cors.allowed_origins_patterns' => 'array',
            'cors.allowed_headers' => 'array',
            'cors.exposed_headers' => 'array',
            'cors.max_age' => 'integer',
            'cors.supports_credentials' => 'boolean',
            'database.default' => 'string',
            'database.connections.sqlite.driver' => 'string',
            'database.connections.sqlite.url' => 'NULL',
            'database.connections.sqlite.database' => 'string',
            'database.connections.sqlite.prefix' => 'string',
            'database.connections.sqlite.foreign_key_constraints' => 'boolean',
            'database.connections.sqlite.busy_timeout' => 'NULL',
            'database.connections.sqlite.journal_mode' => 'NULL',
            'database.connections.sqlite.synchronous' => 'NULL',
            'database.connections.mysql.driver' => 'string',
            'database.connections.mysql.url' => 'NULL',
            'database.connections.mysql.host' => 'string',
            'database.connections.mysql.port' => 'string',
            'database.connections.mysql.database' => 'string',
            'database.connections.mysql.username' => 'string',
            'database.connections.mysql.password' => 'string',
            'database.connections.mysql.unix_socket' => 'string',
            'database.connections.mysql.charset' => 'string',
            'database.connections.mysql.collation' => 'string',
            'database.connections.mysql.prefix' => 'string',
            'database.connections.mysql.prefix_indexes' => 'boolean',
            'database.connections.mysql.strict' => 'boolean',
            'database.connections.mysql.engine' => 'NULL',
            'database.connections.mysql.options' => 'array',
            'database.connections.mariadb.driver' => 'string',
            'database.connections.mariadb.url' => 'NULL',
            'database.connections.mariadb.host' => 'string',
            'database.connections.mariadb.port' => 'string',
            'database.connections.mariadb.database' => 'string',
            'database.connections.mariadb.username' => 'string',
            'database.connections.mariadb.password' => 'string',
            'database.connections.mariadb.unix_socket' => 'string',
            'database.connections.mariadb.charset' => 'string',
            'database.connections.mariadb.collation' => 'string',
            'database.connections.mariadb.prefix' => 'string',
            'database.connections.mariadb.prefix_indexes' => 'boolean',
            'database.connections.mariadb.strict' => 'boolean',
            'database.connections.mariadb.engine' => 'NULL',
            'database.connections.mariadb.options' => 'array',
            'database.connections.pgsql.driver' => 'string',
            'database.connections.pgsql.url' => 'NULL',
            'database.connections.pgsql.host' => 'string',
            'database.connections.pgsql.port' => 'string',
            'database.connections.pgsql.database' => 'string',
            'database.connections.pgsql.username' => 'string',
            'database.connections.pgsql.password' => 'string',
            'database.connections.pgsql.charset' => 'string',
            'database.connections.pgsql.prefix' => 'string',
            'database.connections.pgsql.prefix_indexes' => 'boolean',
            'database.connections.pgsql.search_path' => 'string',
            'database.connections.pgsql.sslmode' => 'string',
            'database.connections.sqlsrv.driver' => 'string',
            'database.connections.sqlsrv.url' => 'NULL',
            'database.connections.sqlsrv.host' => 'string',
            'database.connections.sqlsrv.port' => 'string',
            'database.connections.sqlsrv.database' => 'string',
            'database.connections.sqlsrv.username' => 'string',
            'database.connections.sqlsrv.password' => 'string',
            'database.connections.sqlsrv.charset' => 'string',
            'database.connections.sqlsrv.prefix' => 'string',
            'database.connections.sqlsrv.prefix_indexes' => 'boolean',
            'database.migrations.table' => 'string',
            'database.migrations.update_date_on_publish' => 'boolean',
            'database.redis.client' => 'string',
            'database.redis.options.cluster' => 'string',
            'database.redis.options.prefix' => 'string',
            'database.redis.options.persistent' => 'boolean',
            'database.redis.default.url' => 'NULL',
            'database.redis.default.host' => 'string',
            'database.redis.default.username' => 'NULL',
            'database.redis.default.password' => 'NULL',
            'database.redis.default.port' => 'string',
            'database.redis.default.database' => 'string',
            'database.redis.cache.url' => 'NULL',
            'database.redis.cache.host' => 'string',
            'database.redis.cache.username' => 'NULL',
            'database.redis.cache.password' => 'NULL',
            'database.redis.cache.port' => 'string',
            'database.redis.cache.database' => 'string',
            'database.redis.horizon.url' => 'NULL',
            'database.redis.horizon.host' => 'string',
            'database.redis.horizon.username' => 'NULL',
            'database.redis.horizon.password' => 'NULL',
            'database.redis.horizon.port' => 'string',
            'database.redis.horizon.database' => 'string',
            'database.redis.horizon.options.prefix' => 'string',
            'embeddings.max_audio_length' => 'integer',
            'embeddings.audio_dimension' => 'integer',
            'embeddings.text_dimension' => 'integer',
            'filament.broadcasting' => 'array',
            'filament.default_filesystem_disk' => 'string',
            'filament.assets_path' => 'NULL',
            'filament.cache_path' => 'string',
            'filament.livewire_loading_delay' => 'string',
            'filament.system_route_prefix' => 'string',
            'filesystems.default' => 'string',
            'filesystems.disks.local.driver' => 'string',
            'filesystems.disks.local.root' => 'string',
            'filesystems.disks.local.serve' => 'boolean',
            'filesystems.disks.local.throw' => 'boolean',
            'filesystems.disks.local.report' => 'boolean',
            'filesystems.disks.public.driver' => 'string',
            'filesystems.disks.public.root' => 'string',
            'filesystems.disks.public.url' => 'string',
            'filesystems.disks.public.visibility' => 'string',
            'filesystems.disks.public.throw' => 'boolean',
            'filesystems.disks.public.report' => 'boolean',
            'filesystems.disks.s3.driver' => 'string',
            'filesystems.disks.s3.key' => 'NULL',
            'filesystems.disks.s3.secret' => 'NULL',
            'filesystems.disks.s3.region' => 'NULL',
            'filesystems.disks.s3.bucket' => 'NULL',
            'filesystems.disks.s3.url' => 'NULL',
            'filesystems.disks.s3.endpoint' => 'NULL',
            'filesystems.disks.s3.use_path_style_endpoint' => 'boolean',
            'filesystems.disks.s3.throw' => 'boolean',
            'filesystems.disks.s3.report' => 'boolean',
            'filesystems.disks.tmp.driver' => 'string',
            'filesystems.disks.tmp.root' => 'string',
            'filesystems.links./home/<USER>/projects/pngwasi/smovee/public/storage' => 'string',
            'fortify.guard' => 'string',
            'fortify.middleware' => 'array',
            'fortify.auth_middleware' => 'string',
            'fortify.passwords' => 'string',
            'fortify.username' => 'string',
            'fortify.email' => 'string',
            'fortify.views' => 'boolean',
            'fortify.home' => 'string',
            'fortify.prefix' => 'string',
            'fortify.domain' => 'NULL',
            'fortify.lowercase_usernames' => 'boolean',
            'fortify.limiters.login' => 'string',
            'fortify.limiters.two-factor' => 'string',
            'fortify.paths.login' => 'NULL',
            'fortify.paths.logout' => 'NULL',
            'fortify.paths.password.request' => 'NULL',
            'fortify.paths.password.reset' => 'NULL',
            'fortify.paths.password.email' => 'NULL',
            'fortify.paths.password.update' => 'NULL',
            'fortify.paths.password.confirm' => 'NULL',
            'fortify.paths.password.confirmation' => 'NULL',
            'fortify.paths.register' => 'NULL',
            'fortify.paths.verification.notice' => 'NULL',
            'fortify.paths.verification.verify' => 'NULL',
            'fortify.paths.verification.send' => 'NULL',
            'fortify.paths.user-profile-information.update' => 'NULL',
            'fortify.paths.user-password.update' => 'NULL',
            'fortify.paths.two-factor.login' => 'NULL',
            'fortify.paths.two-factor.enable' => 'NULL',
            'fortify.paths.two-factor.confirm' => 'NULL',
            'fortify.paths.two-factor.disable' => 'NULL',
            'fortify.paths.two-factor.qr-code' => 'NULL',
            'fortify.paths.two-factor.secret-key' => 'NULL',
            'fortify.paths.two-factor.recovery-codes' => 'NULL',
            'fortify.redirects.login' => 'NULL',
            'fortify.redirects.logout' => 'NULL',
            'fortify.redirects.password-confirmation' => 'NULL',
            'fortify.redirects.register' => 'NULL',
            'fortify.redirects.email-verification' => 'NULL',
            'fortify.redirects.password-reset' => 'NULL',
            'fortify.features' => 'array',
            'hashing.driver' => 'string',
            'hashing.bcrypt.rounds' => 'integer',
            'hashing.bcrypt.verify' => 'boolean',
            'hashing.bcrypt.limit' => 'NULL',
            'hashing.argon.memory' => 'integer',
            'hashing.argon.threads' => 'integer',
            'hashing.argon.time' => 'integer',
            'hashing.argon.verify' => 'boolean',
            'hashing.rehash_on_login' => 'boolean',
            'horizon.domain' => 'NULL',
            'horizon.path' => 'string',
            'horizon.use' => 'string',
            'horizon.prefix' => 'string',
            'horizon.middleware' => 'array',
            'horizon.waits.redis:default' => 'integer',
            'horizon.trim.recent' => 'integer',
            'horizon.trim.pending' => 'integer',
            'horizon.trim.completed' => 'integer',
            'horizon.trim.recent_failed' => 'integer',
            'horizon.trim.failed' => 'integer',
            'horizon.trim.monitored' => 'integer',
            'horizon.silenced' => 'array',
            'horizon.metrics.trim_snapshots.job' => 'integer',
            'horizon.metrics.trim_snapshots.queue' => 'integer',
            'horizon.fast_termination' => 'boolean',
            'horizon.memory_limit' => 'integer',
            'horizon.defaults.supervisor-default.connection' => 'string',
            'horizon.defaults.supervisor-default.queue' => 'array',
            'horizon.defaults.supervisor-default.balance' => 'string',
            'horizon.defaults.supervisor-default.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-default.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-default.maxTime' => 'integer',
            'horizon.defaults.supervisor-default.maxJobs' => 'integer',
            'horizon.defaults.supervisor-default.memory' => 'integer',
            'horizon.defaults.supervisor-default.tries' => 'integer',
            'horizon.defaults.supervisor-default.timeout' => 'integer',
            'horizon.defaults.supervisor-default.nice' => 'integer',
            'horizon.defaults.supervisor-recommender.connection' => 'string',
            'horizon.defaults.supervisor-recommender.queue' => 'array',
            'horizon.defaults.supervisor-recommender.balance' => 'string',
            'horizon.defaults.supervisor-recommender.autoScalingStrategy' => 'string',
            'horizon.defaults.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.defaults.supervisor-recommender.maxTime' => 'integer',
            'horizon.defaults.supervisor-recommender.maxJobs' => 'integer',
            'horizon.defaults.supervisor-recommender.memory' => 'integer',
            'horizon.defaults.supervisor-recommender.tries' => 'integer',
            'horizon.defaults.supervisor-recommender.timeout' => 'integer',
            'horizon.defaults.supervisor-recommender.nice' => 'integer',
            'horizon.environments.production.supervisor-default.balance' => 'string',
            'horizon.environments.production.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-default.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-default.balanceCooldown' => 'integer',
            'horizon.environments.production.supervisor-recommender.balance' => 'string',
            'horizon.environments.production.supervisor-recommender.maxProcesses' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceMaxShift' => 'integer',
            'horizon.environments.production.supervisor-recommender.balanceCooldown' => 'integer',
            'horizon.environments.local.supervisor-default.maxProcesses' => 'integer',
            'horizon.environments.local.supervisor-recommender.maxProcesses' => 'integer',
            'ide-helper.filename' => 'string',
            'ide-helper.models_filename' => 'string',
            'ide-helper.meta_filename' => 'string',
            'ide-helper.include_fluent' => 'boolean',
            'ide-helper.include_factory_builders' => 'boolean',
            'ide-helper.write_model_magic_where' => 'boolean',
            'ide-helper.write_model_external_builder_methods' => 'boolean',
            'ide-helper.write_model_relation_count_properties' => 'boolean',
            'ide-helper.write_eloquent_model_mixins' => 'boolean',
            'ide-helper.include_helpers' => 'boolean',
            'ide-helper.helper_files' => 'array',
            'ide-helper.model_locations' => 'array',
            'ide-helper.ignored_models' => 'array',
            'ide-helper.model_hooks' => 'array',
            'ide-helper.extra.Eloquent' => 'array',
            'ide-helper.extra.Session' => 'array',
            'ide-helper.magic' => 'array',
            'ide-helper.interfaces' => 'array',
            'ide-helper.model_camel_case_properties' => 'boolean',
            'ide-helper.type_overrides.integer' => 'string',
            'ide-helper.type_overrides.boolean' => 'string',
            'ide-helper.include_class_docblocks' => 'boolean',
            'ide-helper.force_fqn' => 'boolean',
            'ide-helper.use_generics_annotations' => 'boolean',
            'ide-helper.additional_relation_types' => 'array',
            'ide-helper.additional_relation_return_types' => 'array',
            'ide-helper.enforce_nullable_relationships' => 'boolean',
            'ide-helper.post_migrate' => 'array',
            'ide-helper.macroable_traits' => 'array',
            'ide-helper.custom_db_types' => 'array',
            'laravel-impersonate.session_key' => 'string',
            'laravel-impersonate.session_guard' => 'string',
            'laravel-impersonate.session_guard_using' => 'string',
            'laravel-impersonate.default_impersonator_guard' => 'string',
            'laravel-impersonate.take_redirect_to' => 'string',
            'laravel-impersonate.leave_redirect_to' => 'string',
            'livewire.class_namespace' => 'string',
            'livewire.view_path' => 'string',
            'livewire.layout' => 'string',
            'livewire.lazy_placeholder' => 'NULL',
            'livewire.temporary_file_upload.disk' => 'NULL',
            'livewire.temporary_file_upload.rules' => 'string',
            'livewire.temporary_file_upload.directory' => 'NULL',
            'livewire.temporary_file_upload.middleware' => 'NULL',
            'livewire.temporary_file_upload.preview_mimes' => 'array',
            'livewire.temporary_file_upload.max_upload_time' => 'integer',
            'livewire.render_on_redirect' => 'boolean',
            'livewire.legacy_model_binding' => 'boolean',
            'livewire.inject_assets' => 'boolean',
            'livewire.navigate.show_progress_bar' => 'boolean',
            'livewire.navigate.progress_bar_color' => 'string',
            'livewire.inject_morph_markers' => 'boolean',
            'livewire.pagination_theme' => 'string',
            'livewire.asset_url' => 'NULL',
            'livewire.app_url' => 'NULL',
            'livewire.middleware_group' => 'string',
            'livewire.manifest_path' => 'NULL',
            'livewire.back_button_cache' => 'boolean',
            'logging.default' => 'string',
            'logging.deprecations.channel' => 'NULL',
            'logging.deprecations.trace' => 'boolean',
            'logging.channels.stack.driver' => 'string',
            'logging.channels.stack.channels' => 'array',
            'logging.channels.stack.ignore_exceptions' => 'boolean',
            'logging.channels.single.driver' => 'string',
            'logging.channels.single.path' => 'string',
            'logging.channels.single.level' => 'string',
            'logging.channels.single.replace_placeholders' => 'boolean',
            'logging.channels.daily.driver' => 'string',
            'logging.channels.daily.path' => 'string',
            'logging.channels.daily.level' => 'string',
            'logging.channels.daily.days' => 'integer',
            'logging.channels.daily.replace_placeholders' => 'boolean',
            'logging.channels.slack.driver' => 'string',
            'logging.channels.slack.url' => 'NULL',
            'logging.channels.slack.username' => 'string',
            'logging.channels.slack.emoji' => 'string',
            'logging.channels.slack.level' => 'string',
            'logging.channels.slack.replace_placeholders' => 'boolean',
            'logging.channels.papertrail.driver' => 'string',
            'logging.channels.papertrail.level' => 'string',
            'logging.channels.papertrail.handler' => 'string',
            'logging.channels.papertrail.handler_with.host' => 'NULL',
            'logging.channels.papertrail.handler_with.port' => 'NULL',
            'logging.channels.papertrail.handler_with.connectionString' => 'string',
            'logging.channels.papertrail.processors' => 'array',
            'logging.channels.stderr.driver' => 'string',
            'logging.channels.stderr.level' => 'string',
            'logging.channels.stderr.handler' => 'string',
            'logging.channels.stderr.formatter' => 'NULL',
            'logging.channels.stderr.with.stream' => 'string',
            'logging.channels.stderr.processors' => 'array',
            'logging.channels.syslog.driver' => 'string',
            'logging.channels.syslog.level' => 'string',
            'logging.channels.syslog.facility' => 'integer',
            'logging.channels.syslog.replace_placeholders' => 'boolean',
            'logging.channels.errorlog.driver' => 'string',
            'logging.channels.errorlog.level' => 'string',
            'logging.channels.errorlog.replace_placeholders' => 'boolean',
            'logging.channels.null.driver' => 'string',
            'logging.channels.null.handler' => 'string',
            'logging.channels.emergency.path' => 'string',
            'mail.default' => 'string',
            'mail.mailers.smtp.transport' => 'string',
            'mail.mailers.smtp.scheme' => 'NULL',
            'mail.mailers.smtp.url' => 'NULL',
            'mail.mailers.smtp.host' => 'string',
            'mail.mailers.smtp.port' => 'string',
            'mail.mailers.smtp.username' => 'string',
            'mail.mailers.smtp.password' => 'string',
            'mail.mailers.smtp.timeout' => 'NULL',
            'mail.mailers.smtp.local_domain' => 'string',
            'mail.mailers.ses.transport' => 'string',
            'mail.mailers.postmark.transport' => 'string',
            'mail.mailers.resend.transport' => 'string',
            'mail.mailers.sendmail.transport' => 'string',
            'mail.mailers.sendmail.path' => 'string',
            'mail.mailers.log.transport' => 'string',
            'mail.mailers.log.channel' => 'NULL',
            'mail.mailers.array.transport' => 'string',
            'mail.mailers.failover.transport' => 'string',
            'mail.mailers.failover.mailers' => 'array',
            'mail.mailers.roundrobin.transport' => 'string',
            'mail.mailers.roundrobin.mailers' => 'array',
            'mail.from.address' => 'string',
            'mail.from.name' => 'string',
            'mail.markdown.theme' => 'string',
            'mail.markdown.paths' => 'array',
            'metrics.access_restricted' => 'boolean',
            'metrics.allowed_ips' => 'array',
            'metrics.require_auth' => 'boolean',
            'metrics.require_admin' => 'boolean',
            'metrics.api_token' => 'NULL',
            'metrics.cache_duration' => 'integer',
            'metrics.enabled_metrics.application' => 'boolean',
            'metrics.enabled_metrics.business' => 'boolean',
            'metrics.enabled_metrics.infrastructure' => 'boolean',
            'metrics.enabled_metrics.performance' => 'boolean',
            'metrics.enabled_metrics.recommendations' => 'boolean',
            'metrics.enabled_metrics.audio' => 'boolean',
            'metrics.enabled_metrics.search' => 'boolean',
            'metrics.business.active_user_periods.daily' => 'integer',
            'metrics.business.active_user_periods.weekly' => 'integer',
            'metrics.business.active_user_periods.monthly' => 'integer',
            'metrics.business.new_content_periods.daily' => 'integer',
            'metrics.business.new_content_periods.weekly' => 'integer',
            'metrics.business.new_content_periods.monthly' => 'integer',
            'metrics.business.popular_track_min_plays' => 'integer',
            'metrics.business.popularity_window_days' => 'integer',
            'metrics.performance.enable_query_logging' => 'boolean',
            'metrics.performance.max_slow_queries' => 'integer',
            'metrics.performance.slow_query_threshold' => 'integer',
            'metrics.custom_metrics' => 'array',
            'metrics.thresholds.max_failed_jobs' => 'integer',
            'metrics.thresholds.max_queue_size' => 'integer',
            'metrics.thresholds.min_cache_hit_rate' => 'integer',
            'metrics.thresholds.max_response_time' => 'integer',
            'metrics.thresholds.min_disk_space_gb' => 'integer',
            'octane.server' => 'string',
            'octane.https' => 'boolean',
            'octane.listeners.Laravel\Octane\Events\WorkerStarting' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestHandled' => 'array',
            'octane.listeners.Laravel\Octane\Events\RequestTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TaskTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickReceived' => 'array',
            'octane.listeners.Laravel\Octane\Events\TickTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Contracts\OperationTerminated' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerErrorOccurred' => 'array',
            'octane.listeners.Laravel\Octane\Events\WorkerStopping' => 'array',
            'octane.warm' => 'array',
            'octane.flush' => 'array',
            'octane.tables.example:1000.name' => 'string',
            'octane.tables.example:1000.votes' => 'string',
            'octane.cache.rows' => 'integer',
            'octane.cache.bytes' => 'integer',
            'octane.watch' => 'array',
            'octane.garbage' => 'integer',
            'octane.max_execution_time' => 'integer',
            'ollama-laravel.model' => 'string',
            'ollama-laravel.url' => 'string',
            'ollama-laravel.default_prompt' => 'string',
            'ollama-laravel.connection.timeout' => 'integer',
            'ollama-laravel.auth.type' => 'NULL',
            'ollama-laravel.auth.token' => 'NULL',
            'ollama-laravel.auth.username' => 'NULL',
            'ollama-laravel.auth.password' => 'NULL',
            'ollama-laravel.headers' => 'array',
            'pennant.default' => 'string',
            'pennant.stores.array.driver' => 'string',
            'pennant.stores.database.driver' => 'string',
            'pennant.stores.database.connection' => 'NULL',
            'pennant.stores.database.table' => 'string',
            'pulse.domain' => 'NULL',
            'pulse.path' => 'string',
            'pulse.enabled' => 'boolean',
            'pulse.storage.driver' => 'string',
            'pulse.storage.trim.keep' => 'string',
            'pulse.storage.database.connection' => 'NULL',
            'pulse.storage.database.chunk' => 'integer',
            'pulse.ingest.driver' => 'string',
            'pulse.ingest.buffer' => 'integer',
            'pulse.ingest.trim.lottery' => 'array',
            'pulse.ingest.trim.keep' => 'string',
            'pulse.ingest.redis.connection' => 'NULL',
            'pulse.ingest.redis.chunk' => 'integer',
            'pulse.cache' => 'NULL',
            'pulse.middleware' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\CacheInteractions.groups./^job-exceptions:.*/' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Exceptions.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\Queues.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.server_name' => 'string',
            'pulse.recorders.Laravel\Pulse\Recorders\Servers.directories' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowOutgoingRequests.groups' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.location' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.max_query_length' => 'NULL',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowQueries.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.threshold' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\SlowRequests.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserJobs.ignore' => 'array',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.enabled' => 'boolean',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.sample_rate' => 'integer',
            'pulse.recorders.Laravel\Pulse\Recorders\UserRequests.ignore' => 'array',
            'queue.default' => 'string',
            'queue.connections.sync.driver' => 'string',
            'queue.connections.database.driver' => 'string',
            'queue.connections.database.connection' => 'NULL',
            'queue.connections.database.table' => 'string',
            'queue.connections.database.queue' => 'string',
            'queue.connections.database.retry_after' => 'integer',
            'queue.connections.database.after_commit' => 'boolean',
            'queue.connections.database.timeout' => 'integer',
            'queue.connections.beanstalkd.driver' => 'string',
            'queue.connections.beanstalkd.host' => 'string',
            'queue.connections.beanstalkd.queue' => 'string',
            'queue.connections.beanstalkd.retry_after' => 'integer',
            'queue.connections.beanstalkd.block_for' => 'integer',
            'queue.connections.beanstalkd.after_commit' => 'boolean',
            'queue.connections.sqs.driver' => 'string',
            'queue.connections.sqs.key' => 'NULL',
            'queue.connections.sqs.secret' => 'NULL',
            'queue.connections.sqs.prefix' => 'string',
            'queue.connections.sqs.queue' => 'string',
            'queue.connections.sqs.suffix' => 'NULL',
            'queue.connections.sqs.region' => 'string',
            'queue.connections.sqs.after_commit' => 'boolean',
            'queue.connections.redis.driver' => 'string',
            'queue.connections.redis.connection' => 'string',
            'queue.connections.redis.queue' => 'string',
            'queue.connections.redis.retry_after' => 'integer',
            'queue.connections.redis.block_for' => 'NULL',
            'queue.connections.redis.after_commit' => 'boolean',
            'queue.connections.redis.timeout' => 'integer',
            'queue.batching.database' => 'string',
            'queue.batching.table' => 'string',
            'queue.failed.driver' => 'string',
            'queue.failed.database' => 'string',
            'queue.failed.table' => 'string',
            'recommendations.total_recommendation_items_limit' => 'integer',
            'recommendations.strategy_candidate_limit' => 'integer',
            'recommendations.recent_plays_days' => 'integer',
            'recommendations.popularity_days' => 'integer',
            'recommendations.following_days' => 'integer',
            'recommendations.max_seed_items_per_strategy' => 'integer',
            'recommendations.neighbors_per_seed' => 'integer',
            'recommendations.min_popular_plays' => 'integer',
            'recommendations.min_popular_plays_genre' => 'integer',
            'recommendations.target_playlist_count' => 'integer',
            'recommendations.items_per_playlist' => 'integer',
            'recommendations.playlist_freshness_days' => 'integer',
            'recommendations.max_user_top_genres' => 'integer',
            'recommendations.min_plays_for_top_genre' => 'integer',
            'recommendations.weights.following' => 'double',
            'recommendations.weights.recent_play_similarity' => 'double',
            'recommendations.weights.like_similarity' => 'double',
            'recommendations.weights.popular_genre' => 'double',
            'recommendations.weights.popular_global' => 'double',
            'recommendations.weights.fallback_popular' => 'double',
            'recommendations.weights.collaborative' => 'double',
            'recommendations.weights.top_listened' => 'double',
            'recommendations.diversity.max_artist_ratio' => 'double',
            'recommendations.diversity.min_genres' => 'integer',
            'recommendations.diversity.max_similarity' => 'double',
            'recommendations.diversity.max_embedding_iterations' => 'integer',
            'recommendations.collaborative.enabled' => 'boolean',
            'recommendations.collaborative.min_user_interactions' => 'integer',
            'recommendations.collaborative.max_similar_users' => 'integer',
            'recommendations.collaborative.min_similarity_score' => 'integer',
            'recommendations.collaborative.recommendation_limit' => 'integer',
            'recommendations.enabled_strategies' => 'array',
            'recommendations.recently_played_albums_lookback_days' => 'integer',
            'recommendations.recently_played_albums_limit' => 'integer',
            'recommendations.dj_mix_neighbors_to_fetch' => 'integer',
            'recommendations.dj_mix_exclude_recent_plays_days' => 'integer',
            'recommendations.dj_mix_use_embedding_average_count' => 'integer',
            'recommendations.dj_mix_similarity_vs_diversity_balance' => 'double',
            'recommendations.duplicate_detection.enabled' => 'boolean',
            'recommendations.duplicate_detection.name_similarity_threshold' => 'double',
            'recommendations.duplicate_detection.check_same_artist' => 'boolean',
            'recommendations.duplicate_detection.check_audio_duration' => 'boolean',
            'recommendations.duplicate_detection.duration_tolerance_seconds' => 'integer',
            'recommendations.duplicate_detection.check_audio_metadata' => 'boolean',
            'recommendations.duplicate_detection.tempo_tolerance_bpm' => 'integer',
            'recommendations.duplicate_detection.exclude_duplicate_artists_consecutive' => 'boolean',
            'recommendations.duplicate_detection.max_candidates_to_check' => 'integer',
            'recommendations.top_listened_playlist_enabled' => 'boolean',
            'recommendations.top_listened_playlist_size' => 'integer',
            'recommendations.top_listened_playlist_days' => 'integer',
            'recommendations.top_listened_playlist_min_items' => 'integer',
            'recommendations.top_listened_playlist_title' => 'string',
            'reverb.default' => 'string',
            'reverb.servers.reverb.host' => 'string',
            'reverb.servers.reverb.port' => 'integer',
            'reverb.servers.reverb.hostname' => 'string',
            'reverb.servers.reverb.options.tls' => 'array',
            'reverb.servers.reverb.max_request_size' => 'integer',
            'reverb.servers.reverb.scaling.enabled' => 'boolean',
            'reverb.servers.reverb.scaling.channel' => 'string',
            'reverb.servers.reverb.scaling.server.url' => 'NULL',
            'reverb.servers.reverb.scaling.server.host' => 'string',
            'reverb.servers.reverb.scaling.server.port' => 'string',
            'reverb.servers.reverb.scaling.server.username' => 'NULL',
            'reverb.servers.reverb.scaling.server.password' => 'NULL',
            'reverb.servers.reverb.scaling.server.database' => 'string',
            'reverb.servers.reverb.pulse_ingest_interval' => 'integer',
            'reverb.servers.reverb.telescope_ingest_interval' => 'integer',
            'reverb.apps.provider' => 'string',
            'reverb.apps.apps.0.key' => 'string',
            'reverb.apps.apps.0.secret' => 'string',
            'reverb.apps.apps.0.app_id' => 'string',
            'reverb.apps.apps.0.options.host' => 'string',
            'reverb.apps.apps.0.options.port' => 'string',
            'reverb.apps.apps.0.options.scheme' => 'string',
            'reverb.apps.apps.0.options.useTLS' => 'boolean',
            'reverb.apps.apps.0.allowed_origins' => 'array',
            'reverb.apps.apps.0.ping_interval' => 'integer',
            'reverb.apps.apps.0.activity_timeout' => 'integer',
            'reverb.apps.apps.0.max_message_size' => 'integer',
            'sanctum.stateful' => 'array',
            'sanctum.guard' => 'array',
            'sanctum.expiration' => 'NULL',
            'sanctum.token_prefix' => 'string',
            'sanctum.middleware.authenticate_session' => 'string',
            'sanctum.middleware.encrypt_cookies' => 'string',
            'sanctum.middleware.validate_csrf_token' => 'string',
            'scout.driver' => 'string',
            'scout.prefix' => 'string',
            'scout.queue' => 'boolean',
            'scout.after_commit' => 'boolean',
            'scout.chunk.searchable' => 'integer',
            'scout.chunk.unsearchable' => 'integer',
            'scout.soft_delete' => 'boolean',
            'scout.identify' => 'boolean',
            'scout.algolia.id' => 'string',
            'scout.algolia.secret' => 'string',
            'scout.meilisearch.host' => 'string',
            'scout.meilisearch.key' => 'string',
            'scout.meilisearch.index-settings.App\Models\User.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\User.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Genre.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Channel.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Article.sortableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.filterableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.searchableAttributes' => 'array',
            'scout.meilisearch.index-settings.App\Models\Playlist\Playlist.sortableAttributes' => 'array',
            'scout.typesense.client-settings.api_key' => 'string',
            'scout.typesense.client-settings.nodes.0.host' => 'string',
            'scout.typesense.client-settings.nodes.0.port' => 'string',
            'scout.typesense.client-settings.nodes.0.path' => 'string',
            'scout.typesense.client-settings.nodes.0.protocol' => 'string',
            'scout.typesense.client-settings.nearest_node.host' => 'string',
            'scout.typesense.client-settings.nearest_node.port' => 'string',
            'scout.typesense.client-settings.nearest_node.path' => 'string',
            'scout.typesense.client-settings.nearest_node.protocol' => 'string',
            'scout.typesense.client-settings.connection_timeout_seconds' => 'integer',
            'scout.typesense.client-settings.healthcheck_interval_seconds' => 'integer',
            'scout.typesense.client-settings.num_retries' => 'integer',
            'scout.typesense.client-settings.retry_interval_seconds' => 'integer',
            'scout.typesense.model-settings' => 'array',
            'scramble.api_path' => 'string',
            'scramble.api_domain' => 'NULL',
            'scramble.export_path' => 'string',
            'scramble.info.version' => 'string',
            'scramble.info.description' => 'string',
            'scramble.ui.title' => 'NULL',
            'scramble.ui.theme' => 'string',
            'scramble.ui.hide_try_it' => 'boolean',
            'scramble.ui.hide_schemas' => 'boolean',
            'scramble.ui.logo' => 'string',
            'scramble.ui.try_it_credentials_policy' => 'string',
            'scramble.servers' => 'NULL',
            'scramble.enum_cases_description_strategy' => 'string',
            'scramble.middleware' => 'array',
            'scramble.extensions' => 'array',
            'services.postmark.token' => 'NULL',
            'services.ses.key' => 'NULL',
            'services.ses.secret' => 'NULL',
            'services.ses.region' => 'string',
            'services.resend.key' => 'NULL',
            'services.slack.notifications.bot_user_oauth_token' => 'NULL',
            'services.slack.notifications.channel' => 'NULL',
            'services.google.client_id' => 'string',
            'services.google.client_secret' => 'string',
            'services.google.redirect' => 'string',
            'services.mailgun.domain' => 'NULL',
            'services.mailgun.secret' => 'NULL',
            'services.mailgun.endpoint' => 'string',
            'services.mailgun.scheme' => 'string',
            'services.telegram-bot-api.token' => 'string',
            'services.audio_metadata_extractor.url' => 'NULL',
            'services.audio_embedding_generator.url' => 'string',
            'services.text_embedder.url' => 'NULL',
            'services.image_generator.url' => 'NULL',
            'services.flutterwave.base_url' => 'string',
            'services.flutterwave.secret_key' => 'NULL',
            'services.flutterwave.public_key' => 'NULL',
            'services.flutterwave.encryption_key' => 'NULL',
            'services.flutterwave.is_live' => 'boolean',
            'session.driver' => 'string',
            'session.lifetime' => 'integer',
            'session.expire_on_close' => 'boolean',
            'session.encrypt' => 'boolean',
            'session.files' => 'string',
            'session.connection' => 'NULL',
            'session.table' => 'string',
            'session.store' => 'NULL',
            'session.lottery' => 'array',
            'session.cookie' => 'string',
            'session.path' => 'string',
            'session.domain' => 'NULL',
            'session.secure' => 'NULL',
            'session.http_only' => 'boolean',
            'session.same_site' => 'string',
            'session.partitioned' => 'boolean',
            'telescope.enabled' => 'boolean',
            'telescope.domain' => 'NULL',
            'telescope.path' => 'string',
            'telescope.driver' => 'string',
            'telescope.storage.database.connection' => 'string',
            'telescope.storage.database.chunk' => 'integer',
            'telescope.queue.connection' => 'NULL',
            'telescope.queue.queue' => 'NULL',
            'telescope.queue.delay' => 'integer',
            'telescope.middleware' => 'array',
            'telescope.only_paths' => 'array',
            'telescope.ignore_paths' => 'array',
            'telescope.ignore_commands' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\BatchWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CacheWatcher.hidden' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ClientRequestWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\CommandWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\DumpWatcher.always' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\EventWatcher.ignore' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ExceptionWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_abilities' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\GateWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\JobWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\LogWatcher.level' => 'string',
            'telescope.watchers.Laravel\Telescope\Watchers\MailWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.events' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ModelWatcher.hydrations' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\NotificationWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_packages' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.ignore_paths' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\QueryWatcher.slow' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RedisWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.enabled' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.size_limit' => 'integer',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_http_methods' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\RequestWatcher.ignore_status_codes' => 'array',
            'telescope.watchers.Laravel\Telescope\Watchers\ScheduleWatcher' => 'boolean',
            'telescope.watchers.Laravel\Telescope\Watchers\ViewWatcher' => 'boolean',
            'view.paths' => 'array',
            'view.compiled' => 'string',
            'ziggy.except' => 'array',
            'debugbar.enabled' => 'NULL',
            'debugbar.hide_empty_tabs' => 'boolean',
            'debugbar.except' => 'array',
            'debugbar.storage.enabled' => 'boolean',
            'debugbar.storage.open' => 'NULL',
            'debugbar.storage.driver' => 'string',
            'debugbar.storage.path' => 'string',
            'debugbar.storage.connection' => 'NULL',
            'debugbar.storage.provider' => 'string',
            'debugbar.storage.hostname' => 'string',
            'debugbar.storage.port' => 'integer',
            'debugbar.editor' => 'string',
            'debugbar.remote_sites_path' => 'NULL',
            'debugbar.local_sites_path' => 'NULL',
            'debugbar.include_vendors' => 'boolean',
            'debugbar.capture_ajax' => 'boolean',
            'debugbar.add_ajax_timing' => 'boolean',
            'debugbar.ajax_handler_auto_show' => 'boolean',
            'debugbar.ajax_handler_enable_tab' => 'boolean',
            'debugbar.defer_datasets' => 'boolean',
            'debugbar.error_handler' => 'boolean',
            'debugbar.clockwork' => 'boolean',
            'debugbar.collectors.phpinfo' => 'boolean',
            'debugbar.collectors.messages' => 'boolean',
            'debugbar.collectors.time' => 'boolean',
            'debugbar.collectors.memory' => 'boolean',
            'debugbar.collectors.exceptions' => 'boolean',
            'debugbar.collectors.log' => 'boolean',
            'debugbar.collectors.db' => 'boolean',
            'debugbar.collectors.views' => 'boolean',
            'debugbar.collectors.route' => 'boolean',
            'debugbar.collectors.auth' => 'boolean',
            'debugbar.collectors.gate' => 'boolean',
            'debugbar.collectors.session' => 'boolean',
            'debugbar.collectors.symfony_request' => 'boolean',
            'debugbar.collectors.mail' => 'boolean',
            'debugbar.collectors.laravel' => 'boolean',
            'debugbar.collectors.events' => 'boolean',
            'debugbar.collectors.default_request' => 'boolean',
            'debugbar.collectors.logs' => 'boolean',
            'debugbar.collectors.files' => 'boolean',
            'debugbar.collectors.config' => 'boolean',
            'debugbar.collectors.cache' => 'boolean',
            'debugbar.collectors.models' => 'boolean',
            'debugbar.collectors.livewire' => 'boolean',
            'debugbar.collectors.jobs' => 'boolean',
            'debugbar.collectors.pennant' => 'boolean',
            'debugbar.options.time.memory_usage' => 'boolean',
            'debugbar.options.messages.trace' => 'boolean',
            'debugbar.options.messages.capture_dumps' => 'boolean',
            'debugbar.options.memory.reset_peak' => 'boolean',
            'debugbar.options.memory.with_baseline' => 'boolean',
            'debugbar.options.memory.precision' => 'integer',
            'debugbar.options.auth.show_name' => 'boolean',
            'debugbar.options.auth.show_guards' => 'boolean',
            'debugbar.options.db.with_params' => 'boolean',
            'debugbar.options.db.exclude_paths' => 'array',
            'debugbar.options.db.backtrace' => 'boolean',
            'debugbar.options.db.backtrace_exclude_paths' => 'array',
            'debugbar.options.db.timeline' => 'boolean',
            'debugbar.options.db.duration_background' => 'boolean',
            'debugbar.options.db.explain.enabled' => 'boolean',
            'debugbar.options.db.hints' => 'boolean',
            'debugbar.options.db.show_copy' => 'boolean',
            'debugbar.options.db.slow_threshold' => 'boolean',
            'debugbar.options.db.memory_usage' => 'boolean',
            'debugbar.options.db.soft_limit' => 'integer',
            'debugbar.options.db.hard_limit' => 'integer',
            'debugbar.options.mail.timeline' => 'boolean',
            'debugbar.options.mail.show_body' => 'boolean',
            'debugbar.options.views.timeline' => 'boolean',
            'debugbar.options.views.data' => 'boolean',
            'debugbar.options.views.group' => 'integer',
            'debugbar.options.views.exclude_paths' => 'array',
            'debugbar.options.route.label' => 'boolean',
            'debugbar.options.session.hiddens' => 'array',
            'debugbar.options.symfony_request.label' => 'boolean',
            'debugbar.options.symfony_request.hiddens' => 'array',
            'debugbar.options.events.data' => 'boolean',
            'debugbar.options.logs.file' => 'NULL',
            'debugbar.options.cache.values' => 'boolean',
            'debugbar.inject' => 'boolean',
            'debugbar.route_prefix' => 'string',
            'debugbar.route_middleware' => 'array',
            'debugbar.route_domain' => 'NULL',
            'debugbar.theme' => 'string',
            'debugbar.debug_backtrace_limit' => 'integer',
            'blade-heroicons.prefix' => 'string',
            'blade-heroicons.fallback' => 'string',
            'blade-heroicons.class' => 'string',
            'blade-heroicons.attributes' => 'array',
            'blade-icons.sets' => 'array',
            'blade-icons.class' => 'string',
            'blade-icons.attributes' => 'array',
            'blade-icons.fallback' => 'string',
            'blade-icons.components.disabled' => 'boolean',
            'blade-icons.components.default' => 'string',
            'inertia.ssr.enabled' => 'boolean',
            'inertia.ssr.url' => 'string',
            'inertia.testing.ensure_pages_exist' => 'boolean',
            'inertia.testing.page_paths' => 'array',
            'inertia.testing.page_extensions' => 'array',
            'inertia.history.encrypt' => 'boolean',
            'flare.key' => 'NULL',
            'flare.flare_middleware' => 'array',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddLogs.maximum_number_of_collected_logs' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.maximum_number_of_collected_queries' => 'integer',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddQueries.report_query_bindings' => 'boolean',
            'flare.flare_middleware.Spatie\LaravelIgnition\FlareMiddleware\AddJobs.max_chained_job_reporting_depth' => 'integer',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestBodyFields.censor_fields' => 'array',
            'flare.flare_middleware.Spatie\FlareClient\FlareMiddleware\CensorRequestHeaders.headers' => 'array',
            'flare.send_logs_as_events' => 'boolean',
            'ignition.editor' => 'string',
            'ignition.theme' => 'string',
            'ignition.enable_share_button' => 'boolean',
            'ignition.register_commands' => 'boolean',
            'ignition.solution_providers' => 'array',
            'ignition.ignored_solution_providers' => 'array',
            'ignition.enable_runnable_solutions' => 'NULL',
            'ignition.remote_sites_path' => 'string',
            'ignition.local_sites_path' => 'string',
            'ignition.housekeeping_endpoint_prefix' => 'string',
            'ignition.settings_file_path' => 'string',
            'ignition.recorders' => 'array',
            'ignition.open_ai_key' => 'NULL',
            'ignition.with_stack_frame_arguments' => 'boolean',
            'ignition.argument_reducers' => 'array',
            'tinker.commands' => 'array',
            'tinker.alias' => 'array',
            'tinker.dont_alias' => 'array',
        ]));


    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::mock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::partialMock(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::instance(0), type(1));
    override(\Illuminate\Foundation\Testing\Concerns\InteractsWithContainer::spy(0), map(["" => "@&\Mockery\MockInterface"]));
    override(\Illuminate\Support\Arr::add(0), type(0));
    override(\Illuminate\Support\Arr::except(0), type(0));
    override(\Illuminate\Support\Arr::first(0), elementType(0));
    override(\Illuminate\Support\Arr::last(0), elementType(0));
    override(\Illuminate\Support\Arr::get(0), elementType(0));
    override(\Illuminate\Support\Arr::only(0), type(0));
    override(\Illuminate\Support\Arr::prepend(0), type(0));
    override(\Illuminate\Support\Arr::pull(0), elementType(0));
    override(\Illuminate\Support\Arr::set(0), type(0));
    override(\Illuminate\Support\Arr::shuffle(0), type(0));
    override(\Illuminate\Support\Arr::sort(0), type(0));
    override(\Illuminate\Support\Arr::sortRecursive(0), type(0));
    override(\Illuminate\Support\Arr::where(0), type(0));
    override(\array_add(0), type(0));
    override(\array_except(0), type(0));
    override(\array_first(0), elementType(0));
    override(\array_last(0), elementType(0));
    override(\array_get(0), elementType(0));
    override(\array_only(0), type(0));
    override(\array_prepend(0), type(0));
    override(\array_pull(0), elementType(0));
    override(\array_set(0), type(0));
    override(\array_sort(0), type(0));
    override(\array_sort_recursive(0), type(0));
    override(\array_where(0), type(0));
    override(\head(0), elementType(0));
    override(\last(0), elementType(0));
    override(\with(0), type(0));
    override(\tap(0), type(0));
    override(\optional(0), type(0));

            registerArgumentsSet('auth', 
'viewPulse','viewApiDocs','viewHorizon','viewTelescope',);
        registerArgumentsSet('configs', 
'app.name','app.env','app.debug','app.url','app.frontend_url',
'app.asset_url','app.timezone','app.locale','app.fallback_locale','app.faker_locale',
'app.cipher','app.key','app.previous_keys','app.maintenance.driver','app.maintenance.store',
'app.providers','app.aliases.App','app.aliases.Arr','app.aliases.Artisan','app.aliases.Auth',
'app.aliases.Blade','app.aliases.Broadcast','app.aliases.Bus','app.aliases.Cache','app.aliases.Concurrency',
'app.aliases.Config','app.aliases.Context','app.aliases.Cookie','app.aliases.Crypt','app.aliases.Date',
'app.aliases.DB','app.aliases.Eloquent','app.aliases.Event','app.aliases.File','app.aliases.Gate',
'app.aliases.Hash','app.aliases.Http','app.aliases.Js','app.aliases.Lang','app.aliases.Log',
'app.aliases.Mail','app.aliases.Notification','app.aliases.Number','app.aliases.Password','app.aliases.Process',
'app.aliases.Queue','app.aliases.RateLimiter','app.aliases.Redirect','app.aliases.Request','app.aliases.Response',
'app.aliases.Route','app.aliases.Schedule','app.aliases.Schema','app.aliases.Session','app.aliases.Storage',
'app.aliases.Str','app.aliases.URL','app.aliases.Uri','app.aliases.Validator','app.aliases.View',
'app.aliases.Vite','app.in_container','audio.hls_enabled','audio.hls_bitrates.low','audio.hls_bitrates.medium',
'audio.hls_bitrates.high','audio.hls_segment_length','auth.defaults.guard','auth.defaults.passwords','auth.guards.web.driver',
'auth.guards.web.provider','auth.guards.sanctum.driver','auth.guards.sanctum.provider','auth.providers.users.driver','auth.providers.users.model',
'auth.passwords.users.provider','auth.passwords.users.table','auth.passwords.users.expire','auth.passwords.users.throttle','auth.password_timeout',
'backup.backup.name','backup.backup.source.files.include','backup.backup.source.files.exclude','backup.backup.source.files.follow_links','backup.backup.source.files.ignore_unreadable_directories',
'backup.backup.source.files.relative_path','backup.backup.source.databases','backup.backup.database_dump_compressor','backup.backup.database_dump_file_timestamp_format','backup.backup.database_dump_file_extension',
'backup.backup.destination.compression_method','backup.backup.destination.compression_level','backup.backup.destination.filename_prefix','backup.backup.destination.disks','backup.backup.temporary_directory',
'backup.backup.password','backup.backup.encryption','backup.backup.tries','backup.backup.retry_delay','backup.notifications.notifications.App\\Notifications\\Backup\\BackupHasFailedNotification',
'backup.notifications.notifications.App\\Notifications\\Backup\\UnhealthyBackupWasFoundNotification','backup.notifications.notifications.App\\Notifications\\Backup\\CleanupHasFailedNotification','backup.notifications.notifications.App\\Notifications\\Backup\\BackupWasSuccessfulNotification','backup.notifications.notifications.App\\Notifications\\Backup\\HealthyBackupWasFoundNotification','backup.notifications.notifications.App\\Notifications\\Backup\\CleanupWasSuccessfulNotification',
'backup.notifications.notifiable','backup.notifications.mail.to','backup.notifications.mail.from.address','backup.notifications.mail.from.name','backup.notifications.telegram.chat_id',
'backup.notifications.slack.webhook_url','backup.notifications.slack.channel','backup.notifications.slack.username','backup.notifications.slack.icon','backup.notifications.discord.webhook_url',
'backup.notifications.discord.username','backup.notifications.discord.avatar_url','backup.monitor_backups.0.name','backup.monitor_backups.0.disks','backup.monitor_backups.0.health_checks.Spatie\\Backup\\Tasks\\Monitor\\HealthChecks\\MaximumAgeInDays',
'backup.monitor_backups.0.health_checks.Spatie\\Backup\\Tasks\\Monitor\\HealthChecks\\MaximumStorageInMegabytes','backup.cleanup.strategy','backup.cleanup.default_strategy.keep_all_backups_for_days','backup.cleanup.default_strategy.keep_daily_backups_for_days','backup.cleanup.default_strategy.keep_weekly_backups_for_weeks',
'backup.cleanup.default_strategy.keep_monthly_backups_for_months','backup.cleanup.default_strategy.keep_yearly_backups_for_years','backup.cleanup.default_strategy.delete_oldest_backups_when_using_more_megabytes_than','backup.cleanup.tries','backup.cleanup.retry_delay',
'broadcasting.default','broadcasting.connections.reverb.driver','broadcasting.connections.reverb.key','broadcasting.connections.reverb.secret','broadcasting.connections.reverb.app_id',
'broadcasting.connections.reverb.options.host','broadcasting.connections.reverb.options.port','broadcasting.connections.reverb.options.scheme','broadcasting.connections.reverb.options.useTLS','broadcasting.connections.reverb.client_options',
'broadcasting.connections.pusher.driver','broadcasting.connections.pusher.key','broadcasting.connections.pusher.secret','broadcasting.connections.pusher.app_id','broadcasting.connections.pusher.options.cluster',
'broadcasting.connections.pusher.options.host','broadcasting.connections.pusher.options.port','broadcasting.connections.pusher.options.scheme','broadcasting.connections.pusher.options.encrypted','broadcasting.connections.pusher.options.useTLS',
'broadcasting.connections.pusher.client_options','broadcasting.connections.ably.driver','broadcasting.connections.ably.key','broadcasting.connections.log.driver','broadcasting.connections.null.driver',
'cache.default','cache.stores.array.driver','cache.stores.array.serialize','cache.stores.database.driver','cache.stores.database.connection',
'cache.stores.database.table','cache.stores.database.lock_connection','cache.stores.database.lock_table','cache.stores.file.driver','cache.stores.file.path',
'cache.stores.file.lock_path','cache.stores.memcached.driver','cache.stores.memcached.persistent_id','cache.stores.memcached.sasl','cache.stores.memcached.options',
'cache.stores.memcached.servers.0.host','cache.stores.memcached.servers.0.port','cache.stores.memcached.servers.0.weight','cache.stores.redis.driver','cache.stores.redis.connection',
'cache.stores.redis.lock_connection','cache.stores.dynamodb.driver','cache.stores.dynamodb.key','cache.stores.dynamodb.secret','cache.stores.dynamodb.region',
'cache.stores.dynamodb.table','cache.stores.dynamodb.endpoint','cache.stores.octane.driver','cache.prefix','concurrency.default',
'cors.paths','cors.allowed_methods','cors.allowed_origins','cors.allowed_origins_patterns','cors.allowed_headers',
'cors.exposed_headers','cors.max_age','cors.supports_credentials','database.default','database.connections.sqlite.driver',
'database.connections.sqlite.url','database.connections.sqlite.database','database.connections.sqlite.prefix','database.connections.sqlite.foreign_key_constraints','database.connections.sqlite.busy_timeout',
'database.connections.sqlite.journal_mode','database.connections.sqlite.synchronous','database.connections.mysql.driver','database.connections.mysql.url','database.connections.mysql.host',
'database.connections.mysql.port','database.connections.mysql.database','database.connections.mysql.username','database.connections.mysql.password','database.connections.mysql.unix_socket',
'database.connections.mysql.charset','database.connections.mysql.collation','database.connections.mysql.prefix','database.connections.mysql.prefix_indexes','database.connections.mysql.strict',
'database.connections.mysql.engine','database.connections.mysql.options','database.connections.mariadb.driver','database.connections.mariadb.url','database.connections.mariadb.host',
'database.connections.mariadb.port','database.connections.mariadb.database','database.connections.mariadb.username','database.connections.mariadb.password','database.connections.mariadb.unix_socket',
'database.connections.mariadb.charset','database.connections.mariadb.collation','database.connections.mariadb.prefix','database.connections.mariadb.prefix_indexes','database.connections.mariadb.strict',
'database.connections.mariadb.engine','database.connections.mariadb.options','database.connections.pgsql.driver','database.connections.pgsql.url','database.connections.pgsql.host',
'database.connections.pgsql.port','database.connections.pgsql.database','database.connections.pgsql.username','database.connections.pgsql.password','database.connections.pgsql.charset',
'database.connections.pgsql.prefix','database.connections.pgsql.prefix_indexes','database.connections.pgsql.search_path','database.connections.pgsql.sslmode','database.connections.sqlsrv.driver',
'database.connections.sqlsrv.url','database.connections.sqlsrv.host','database.connections.sqlsrv.port','database.connections.sqlsrv.database','database.connections.sqlsrv.username',
'database.connections.sqlsrv.password','database.connections.sqlsrv.charset','database.connections.sqlsrv.prefix','database.connections.sqlsrv.prefix_indexes','database.migrations.table',
'database.migrations.update_date_on_publish','database.redis.client','database.redis.options.cluster','database.redis.options.prefix','database.redis.options.persistent',
'database.redis.default.url','database.redis.default.host','database.redis.default.username','database.redis.default.password','database.redis.default.port',
'database.redis.default.database','database.redis.cache.url','database.redis.cache.host','database.redis.cache.username','database.redis.cache.password',
'database.redis.cache.port','database.redis.cache.database','database.redis.horizon.url','database.redis.horizon.host','database.redis.horizon.username',
'database.redis.horizon.password','database.redis.horizon.port','database.redis.horizon.database','database.redis.horizon.options.prefix','embeddings.max_audio_length',
'embeddings.audio_dimension','embeddings.text_dimension','filament.broadcasting','filament.default_filesystem_disk','filament.assets_path',
'filament.cache_path','filament.livewire_loading_delay','filament.system_route_prefix','filesystems.default','filesystems.disks.local.driver',
'filesystems.disks.local.root','filesystems.disks.local.serve','filesystems.disks.local.throw','filesystems.disks.local.report','filesystems.disks.public.driver',
'filesystems.disks.public.root','filesystems.disks.public.url','filesystems.disks.public.visibility','filesystems.disks.public.throw','filesystems.disks.public.report',
'filesystems.disks.s3.driver','filesystems.disks.s3.key','filesystems.disks.s3.secret','filesystems.disks.s3.region','filesystems.disks.s3.bucket',
'filesystems.disks.s3.url','filesystems.disks.s3.endpoint','filesystems.disks.s3.use_path_style_endpoint','filesystems.disks.s3.throw','filesystems.disks.s3.report',
'filesystems.disks.tmp.driver','filesystems.disks.tmp.root','filesystems.links./home/<USER>/projects/pngwasi/smovee/public/storage','fortify.guard','fortify.middleware',
'fortify.auth_middleware','fortify.passwords','fortify.username','fortify.email','fortify.views',
'fortify.home','fortify.prefix','fortify.domain','fortify.lowercase_usernames','fortify.limiters.login',
'fortify.limiters.two-factor','fortify.paths.login','fortify.paths.logout','fortify.paths.password.request','fortify.paths.password.reset',
'fortify.paths.password.email','fortify.paths.password.update','fortify.paths.password.confirm','fortify.paths.password.confirmation','fortify.paths.register',
'fortify.paths.verification.notice','fortify.paths.verification.verify','fortify.paths.verification.send','fortify.paths.user-profile-information.update','fortify.paths.user-password.update',
'fortify.paths.two-factor.login','fortify.paths.two-factor.enable','fortify.paths.two-factor.confirm','fortify.paths.two-factor.disable','fortify.paths.two-factor.qr-code',
'fortify.paths.two-factor.secret-key','fortify.paths.two-factor.recovery-codes','fortify.redirects.login','fortify.redirects.logout','fortify.redirects.password-confirmation',
'fortify.redirects.register','fortify.redirects.email-verification','fortify.redirects.password-reset','fortify.features','hashing.driver',
'hashing.bcrypt.rounds','hashing.bcrypt.verify','hashing.bcrypt.limit','hashing.argon.memory','hashing.argon.threads',
'hashing.argon.time','hashing.argon.verify','hashing.rehash_on_login','horizon.domain','horizon.path',
'horizon.use','horizon.prefix','horizon.middleware','horizon.waits.redis:default','horizon.trim.recent',
'horizon.trim.pending','horizon.trim.completed','horizon.trim.recent_failed','horizon.trim.failed','horizon.trim.monitored',
'horizon.silenced','horizon.metrics.trim_snapshots.job','horizon.metrics.trim_snapshots.queue','horizon.fast_termination','horizon.memory_limit',
'horizon.defaults.supervisor-default.connection','horizon.defaults.supervisor-default.queue','horizon.defaults.supervisor-default.balance','horizon.defaults.supervisor-default.autoScalingStrategy','horizon.defaults.supervisor-default.maxProcesses',
'horizon.defaults.supervisor-default.maxTime','horizon.defaults.supervisor-default.maxJobs','horizon.defaults.supervisor-default.memory','horizon.defaults.supervisor-default.tries','horizon.defaults.supervisor-default.timeout',
'horizon.defaults.supervisor-default.nice','horizon.defaults.supervisor-recommender.connection','horizon.defaults.supervisor-recommender.queue','horizon.defaults.supervisor-recommender.balance','horizon.defaults.supervisor-recommender.autoScalingStrategy',
'horizon.defaults.supervisor-recommender.maxProcesses','horizon.defaults.supervisor-recommender.maxTime','horizon.defaults.supervisor-recommender.maxJobs','horizon.defaults.supervisor-recommender.memory','horizon.defaults.supervisor-recommender.tries',
'horizon.defaults.supervisor-recommender.timeout','horizon.defaults.supervisor-recommender.nice','horizon.environments.production.supervisor-default.balance','horizon.environments.production.supervisor-default.maxProcesses','horizon.environments.production.supervisor-default.balanceMaxShift',
'horizon.environments.production.supervisor-default.balanceCooldown','horizon.environments.production.supervisor-recommender.balance','horizon.environments.production.supervisor-recommender.maxProcesses','horizon.environments.production.supervisor-recommender.balanceMaxShift','horizon.environments.production.supervisor-recommender.balanceCooldown',
'horizon.environments.local.supervisor-default.maxProcesses','horizon.environments.local.supervisor-recommender.maxProcesses','ide-helper.filename','ide-helper.models_filename','ide-helper.meta_filename',
'ide-helper.include_fluent','ide-helper.include_factory_builders','ide-helper.write_model_magic_where','ide-helper.write_model_external_builder_methods','ide-helper.write_model_relation_count_properties',
'ide-helper.write_eloquent_model_mixins','ide-helper.include_helpers','ide-helper.helper_files','ide-helper.model_locations','ide-helper.ignored_models',
'ide-helper.model_hooks','ide-helper.extra.Eloquent','ide-helper.extra.Session','ide-helper.magic','ide-helper.interfaces',
'ide-helper.model_camel_case_properties','ide-helper.type_overrides.integer','ide-helper.type_overrides.boolean','ide-helper.include_class_docblocks','ide-helper.force_fqn',
'ide-helper.use_generics_annotations','ide-helper.additional_relation_types','ide-helper.additional_relation_return_types','ide-helper.enforce_nullable_relationships','ide-helper.post_migrate',
'ide-helper.macroable_traits','ide-helper.custom_db_types','laravel-impersonate.session_key','laravel-impersonate.session_guard','laravel-impersonate.session_guard_using',
'laravel-impersonate.default_impersonator_guard','laravel-impersonate.take_redirect_to','laravel-impersonate.leave_redirect_to','livewire.class_namespace','livewire.view_path',
'livewire.layout','livewire.lazy_placeholder','livewire.temporary_file_upload.disk','livewire.temporary_file_upload.rules','livewire.temporary_file_upload.directory',
'livewire.temporary_file_upload.middleware','livewire.temporary_file_upload.preview_mimes','livewire.temporary_file_upload.max_upload_time','livewire.render_on_redirect','livewire.legacy_model_binding',
'livewire.inject_assets','livewire.navigate.show_progress_bar','livewire.navigate.progress_bar_color','livewire.inject_morph_markers','livewire.pagination_theme',
'livewire.asset_url','livewire.app_url','livewire.middleware_group','livewire.manifest_path','livewire.back_button_cache',
'logging.default','logging.deprecations.channel','logging.deprecations.trace','logging.channels.stack.driver','logging.channels.stack.channels',
'logging.channels.stack.ignore_exceptions','logging.channels.single.driver','logging.channels.single.path','logging.channels.single.level','logging.channels.single.replace_placeholders',
'logging.channels.daily.driver','logging.channels.daily.path','logging.channels.daily.level','logging.channels.daily.days','logging.channels.daily.replace_placeholders',
'logging.channels.slack.driver','logging.channels.slack.url','logging.channels.slack.username','logging.channels.slack.emoji','logging.channels.slack.level',
'logging.channels.slack.replace_placeholders','logging.channels.papertrail.driver','logging.channels.papertrail.level','logging.channels.papertrail.handler','logging.channels.papertrail.handler_with.host',
'logging.channels.papertrail.handler_with.port','logging.channels.papertrail.handler_with.connectionString','logging.channels.papertrail.processors','logging.channels.stderr.driver','logging.channels.stderr.level',
'logging.channels.stderr.handler','logging.channels.stderr.formatter','logging.channels.stderr.with.stream','logging.channels.stderr.processors','logging.channels.syslog.driver',
'logging.channels.syslog.level','logging.channels.syslog.facility','logging.channels.syslog.replace_placeholders','logging.channels.errorlog.driver','logging.channels.errorlog.level',
'logging.channels.errorlog.replace_placeholders','logging.channels.null.driver','logging.channels.null.handler','logging.channels.emergency.path','mail.default',
'mail.mailers.smtp.transport','mail.mailers.smtp.scheme','mail.mailers.smtp.url','mail.mailers.smtp.host','mail.mailers.smtp.port',
'mail.mailers.smtp.username','mail.mailers.smtp.password','mail.mailers.smtp.timeout','mail.mailers.smtp.local_domain','mail.mailers.ses.transport',
'mail.mailers.postmark.transport','mail.mailers.resend.transport','mail.mailers.sendmail.transport','mail.mailers.sendmail.path','mail.mailers.log.transport',
'mail.mailers.log.channel','mail.mailers.array.transport','mail.mailers.failover.transport','mail.mailers.failover.mailers','mail.mailers.roundrobin.transport',
'mail.mailers.roundrobin.mailers','mail.from.address','mail.from.name','mail.markdown.theme','mail.markdown.paths',
'metrics.access_restricted','metrics.allowed_ips','metrics.require_auth','metrics.require_admin','metrics.api_token',
'metrics.cache_duration','metrics.enabled_metrics.application','metrics.enabled_metrics.business','metrics.enabled_metrics.infrastructure','metrics.enabled_metrics.performance',
'metrics.enabled_metrics.recommendations','metrics.enabled_metrics.audio','metrics.enabled_metrics.search','metrics.business.active_user_periods.daily','metrics.business.active_user_periods.weekly',
'metrics.business.active_user_periods.monthly','metrics.business.new_content_periods.daily','metrics.business.new_content_periods.weekly','metrics.business.new_content_periods.monthly','metrics.business.popular_track_min_plays',
'metrics.business.popularity_window_days','metrics.performance.enable_query_logging','metrics.performance.max_slow_queries','metrics.performance.slow_query_threshold','metrics.custom_metrics',
'metrics.thresholds.max_failed_jobs','metrics.thresholds.max_queue_size','metrics.thresholds.min_cache_hit_rate','metrics.thresholds.max_response_time','metrics.thresholds.min_disk_space_gb',
'octane.server','octane.https','octane.listeners.Laravel\\Octane\\Events\\WorkerStarting','octane.listeners.Laravel\\Octane\\Events\\RequestReceived','octane.listeners.Laravel\\Octane\\Events\\RequestHandled',
'octane.listeners.Laravel\\Octane\\Events\\RequestTerminated','octane.listeners.Laravel\\Octane\\Events\\TaskReceived','octane.listeners.Laravel\\Octane\\Events\\TaskTerminated','octane.listeners.Laravel\\Octane\\Events\\TickReceived','octane.listeners.Laravel\\Octane\\Events\\TickTerminated',
'octane.listeners.Laravel\\Octane\\Contracts\\OperationTerminated','octane.listeners.Laravel\\Octane\\Events\\WorkerErrorOccurred','octane.listeners.Laravel\\Octane\\Events\\WorkerStopping','octane.warm','octane.flush',
'octane.tables.example:1000.name','octane.tables.example:1000.votes','octane.cache.rows','octane.cache.bytes','octane.watch',
'octane.garbage','octane.max_execution_time','ollama-laravel.model','ollama-laravel.url','ollama-laravel.default_prompt',
'ollama-laravel.connection.timeout','ollama-laravel.auth.type','ollama-laravel.auth.token','ollama-laravel.auth.username','ollama-laravel.auth.password',
'ollama-laravel.headers','pennant.default','pennant.stores.array.driver','pennant.stores.database.driver','pennant.stores.database.connection',
'pennant.stores.database.table','pulse.domain','pulse.path','pulse.enabled','pulse.storage.driver',
'pulse.storage.trim.keep','pulse.storage.database.connection','pulse.storage.database.chunk','pulse.ingest.driver','pulse.ingest.buffer',
'pulse.ingest.trim.lottery','pulse.ingest.trim.keep','pulse.ingest.redis.connection','pulse.ingest.redis.chunk','pulse.cache',
'pulse.middleware','pulse.recorders.Laravel\\Pulse\\Recorders\\CacheInteractions.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\CacheInteractions.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\CacheInteractions.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\CacheInteractions.groups./^job-exceptions:.*/',
'pulse.recorders.Laravel\\Pulse\\Recorders\\Exceptions.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\Exceptions.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\Exceptions.location','pulse.recorders.Laravel\\Pulse\\Recorders\\Exceptions.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\Queues.enabled',
'pulse.recorders.Laravel\\Pulse\\Recorders\\Queues.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\Queues.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\Servers.server_name','pulse.recorders.Laravel\\Pulse\\Recorders\\Servers.directories','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowJobs.enabled',
'pulse.recorders.Laravel\\Pulse\\Recorders\\SlowJobs.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowJobs.threshold','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowJobs.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowOutgoingRequests.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowOutgoingRequests.sample_rate',
'pulse.recorders.Laravel\\Pulse\\Recorders\\SlowOutgoingRequests.threshold','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowOutgoingRequests.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowOutgoingRequests.groups','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.sample_rate',
'pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.threshold','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.location','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.max_query_length','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowQueries.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowRequests.enabled',
'pulse.recorders.Laravel\\Pulse\\Recorders\\SlowRequests.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowRequests.threshold','pulse.recorders.Laravel\\Pulse\\Recorders\\SlowRequests.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\UserJobs.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\UserJobs.sample_rate',
'pulse.recorders.Laravel\\Pulse\\Recorders\\UserJobs.ignore','pulse.recorders.Laravel\\Pulse\\Recorders\\UserRequests.enabled','pulse.recorders.Laravel\\Pulse\\Recorders\\UserRequests.sample_rate','pulse.recorders.Laravel\\Pulse\\Recorders\\UserRequests.ignore','queue.default',
'queue.connections.sync.driver','queue.connections.database.driver','queue.connections.database.connection','queue.connections.database.table','queue.connections.database.queue',
'queue.connections.database.retry_after','queue.connections.database.after_commit','queue.connections.database.timeout','queue.connections.beanstalkd.driver','queue.connections.beanstalkd.host',
'queue.connections.beanstalkd.queue','queue.connections.beanstalkd.retry_after','queue.connections.beanstalkd.block_for','queue.connections.beanstalkd.after_commit','queue.connections.sqs.driver',
'queue.connections.sqs.key','queue.connections.sqs.secret','queue.connections.sqs.prefix','queue.connections.sqs.queue','queue.connections.sqs.suffix',
'queue.connections.sqs.region','queue.connections.sqs.after_commit','queue.connections.redis.driver','queue.connections.redis.connection','queue.connections.redis.queue',
'queue.connections.redis.retry_after','queue.connections.redis.block_for','queue.connections.redis.after_commit','queue.connections.redis.timeout','queue.batching.database',
'queue.batching.table','queue.failed.driver','queue.failed.database','queue.failed.table','recommendations.total_recommendation_items_limit',
'recommendations.strategy_candidate_limit','recommendations.recent_plays_days','recommendations.popularity_days','recommendations.following_days','recommendations.max_seed_items_per_strategy',
'recommendations.neighbors_per_seed','recommendations.min_popular_plays','recommendations.min_popular_plays_genre','recommendations.target_playlist_count','recommendations.items_per_playlist',
'recommendations.playlist_freshness_days','recommendations.max_user_top_genres','recommendations.min_plays_for_top_genre','recommendations.weights.following','recommendations.weights.recent_play_similarity',
'recommendations.weights.like_similarity','recommendations.weights.popular_genre','recommendations.weights.popular_global','recommendations.weights.fallback_popular','recommendations.weights.collaborative',
'recommendations.weights.top_listened','recommendations.diversity.max_artist_ratio','recommendations.diversity.min_genres','recommendations.diversity.max_similarity','recommendations.diversity.max_embedding_iterations',
'recommendations.collaborative.enabled','recommendations.collaborative.min_user_interactions','recommendations.collaborative.max_similar_users','recommendations.collaborative.min_similarity_score','recommendations.collaborative.recommendation_limit',
'recommendations.enabled_strategies','recommendations.recently_played_albums_lookback_days','recommendations.recently_played_albums_limit','recommendations.dj_mix_neighbors_to_fetch','recommendations.dj_mix_exclude_recent_plays_days',
'recommendations.dj_mix_use_embedding_average_count','recommendations.dj_mix_similarity_vs_diversity_balance','recommendations.duplicate_detection.enabled','recommendations.duplicate_detection.name_similarity_threshold','recommendations.duplicate_detection.check_same_artist',
'recommendations.duplicate_detection.check_audio_duration','recommendations.duplicate_detection.duration_tolerance_seconds','recommendations.duplicate_detection.check_audio_metadata','recommendations.duplicate_detection.tempo_tolerance_bpm','recommendations.duplicate_detection.exclude_duplicate_artists_consecutive',
'recommendations.duplicate_detection.max_candidates_to_check','recommendations.top_listened_playlist_enabled','recommendations.top_listened_playlist_size','recommendations.top_listened_playlist_days','recommendations.top_listened_playlist_min_items',
'recommendations.top_listened_playlist_title','reverb.default','reverb.servers.reverb.host','reverb.servers.reverb.port','reverb.servers.reverb.hostname',
'reverb.servers.reverb.options.tls','reverb.servers.reverb.max_request_size','reverb.servers.reverb.scaling.enabled','reverb.servers.reverb.scaling.channel','reverb.servers.reverb.scaling.server.url',
'reverb.servers.reverb.scaling.server.host','reverb.servers.reverb.scaling.server.port','reverb.servers.reverb.scaling.server.username','reverb.servers.reverb.scaling.server.password','reverb.servers.reverb.scaling.server.database',
'reverb.servers.reverb.pulse_ingest_interval','reverb.servers.reverb.telescope_ingest_interval','reverb.apps.provider','reverb.apps.apps.0.key','reverb.apps.apps.0.secret',
'reverb.apps.apps.0.app_id','reverb.apps.apps.0.options.host','reverb.apps.apps.0.options.port','reverb.apps.apps.0.options.scheme','reverb.apps.apps.0.options.useTLS',
'reverb.apps.apps.0.allowed_origins','reverb.apps.apps.0.ping_interval','reverb.apps.apps.0.activity_timeout','reverb.apps.apps.0.max_message_size','sanctum.stateful',
'sanctum.guard','sanctum.expiration','sanctum.token_prefix','sanctum.middleware.authenticate_session','sanctum.middleware.encrypt_cookies',
'sanctum.middleware.validate_csrf_token','scout.driver','scout.prefix','scout.queue','scout.after_commit',
'scout.chunk.searchable','scout.chunk.unsearchable','scout.soft_delete','scout.identify','scout.algolia.id',
'scout.algolia.secret','scout.meilisearch.host','scout.meilisearch.key','scout.meilisearch.index-settings.App\\Models\\User.filterableAttributes','scout.meilisearch.index-settings.App\\Models\\User.searchableAttributes',
'scout.meilisearch.index-settings.App\\Models\\User.sortableAttributes','scout.meilisearch.index-settings.App\\Models\\Genre.filterableAttributes','scout.meilisearch.index-settings.App\\Models\\Genre.searchableAttributes','scout.meilisearch.index-settings.App\\Models\\Genre.sortableAttributes','scout.meilisearch.index-settings.App\\Models\\Channel.filterableAttributes',
'scout.meilisearch.index-settings.App\\Models\\Channel.searchableAttributes','scout.meilisearch.index-settings.App\\Models\\Channel.sortableAttributes','scout.meilisearch.index-settings.App\\Models\\Article.filterableAttributes','scout.meilisearch.index-settings.App\\Models\\Article.searchableAttributes','scout.meilisearch.index-settings.App\\Models\\Article.sortableAttributes',
'scout.meilisearch.index-settings.App\\Models\\Playlist\\Playlist.filterableAttributes','scout.meilisearch.index-settings.App\\Models\\Playlist\\Playlist.searchableAttributes','scout.meilisearch.index-settings.App\\Models\\Playlist\\Playlist.sortableAttributes','scout.typesense.client-settings.api_key','scout.typesense.client-settings.nodes.0.host',
'scout.typesense.client-settings.nodes.0.port','scout.typesense.client-settings.nodes.0.path','scout.typesense.client-settings.nodes.0.protocol','scout.typesense.client-settings.nearest_node.host','scout.typesense.client-settings.nearest_node.port',
'scout.typesense.client-settings.nearest_node.path','scout.typesense.client-settings.nearest_node.protocol','scout.typesense.client-settings.connection_timeout_seconds','scout.typesense.client-settings.healthcheck_interval_seconds','scout.typesense.client-settings.num_retries',
'scout.typesense.client-settings.retry_interval_seconds','scout.typesense.model-settings','scramble.api_path','scramble.api_domain','scramble.export_path',
'scramble.info.version','scramble.info.description','scramble.ui.title','scramble.ui.theme','scramble.ui.hide_try_it',
'scramble.ui.hide_schemas','scramble.ui.logo','scramble.ui.try_it_credentials_policy','scramble.servers','scramble.enum_cases_description_strategy',
'scramble.middleware','scramble.extensions','services.postmark.token','services.ses.key','services.ses.secret',
'services.ses.region','services.resend.key','services.slack.notifications.bot_user_oauth_token','services.slack.notifications.channel','services.google.client_id',
'services.google.client_secret','services.google.redirect','services.mailgun.domain','services.mailgun.secret','services.mailgun.endpoint',
'services.mailgun.scheme','services.telegram-bot-api.token','services.audio_metadata_extractor.url','services.audio_embedding_generator.url','services.text_embedder.url',
'services.image_generator.url','services.flutterwave.base_url','services.flutterwave.secret_key','services.flutterwave.public_key','services.flutterwave.encryption_key',
'services.flutterwave.is_live','session.driver','session.lifetime','session.expire_on_close','session.encrypt',
'session.files','session.connection','session.table','session.store','session.lottery',
'session.cookie','session.path','session.domain','session.secure','session.http_only',
'session.same_site','session.partitioned','telescope.enabled','telescope.domain','telescope.path',
'telescope.driver','telescope.storage.database.connection','telescope.storage.database.chunk','telescope.queue.connection','telescope.queue.queue',
'telescope.queue.delay','telescope.middleware','telescope.only_paths','telescope.ignore_paths','telescope.ignore_commands',
'telescope.watchers.Laravel\\Telescope\\Watchers\\BatchWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\CacheWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\CacheWatcher.hidden','telescope.watchers.Laravel\\Telescope\\Watchers\\ClientRequestWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\CommandWatcher.enabled',
'telescope.watchers.Laravel\\Telescope\\Watchers\\CommandWatcher.ignore','telescope.watchers.Laravel\\Telescope\\Watchers\\DumpWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\DumpWatcher.always','telescope.watchers.Laravel\\Telescope\\Watchers\\EventWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\EventWatcher.ignore',
'telescope.watchers.Laravel\\Telescope\\Watchers\\ExceptionWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.ignore_abilities','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.ignore_packages','telescope.watchers.Laravel\\Telescope\\Watchers\\GateWatcher.ignore_paths',
'telescope.watchers.Laravel\\Telescope\\Watchers\\JobWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\LogWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\LogWatcher.level','telescope.watchers.Laravel\\Telescope\\Watchers\\MailWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\ModelWatcher.enabled',
'telescope.watchers.Laravel\\Telescope\\Watchers\\ModelWatcher.events','telescope.watchers.Laravel\\Telescope\\Watchers\\ModelWatcher.hydrations','telescope.watchers.Laravel\\Telescope\\Watchers\\NotificationWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.ignore_packages',
'telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.ignore_paths','telescope.watchers.Laravel\\Telescope\\Watchers\\QueryWatcher.slow','telescope.watchers.Laravel\\Telescope\\Watchers\\RedisWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.enabled','telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.size_limit',
'telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.ignore_http_methods','telescope.watchers.Laravel\\Telescope\\Watchers\\RequestWatcher.ignore_status_codes','telescope.watchers.Laravel\\Telescope\\Watchers\\ScheduleWatcher','telescope.watchers.Laravel\\Telescope\\Watchers\\ViewWatcher','view.paths',
'view.compiled','ziggy.except','debugbar.enabled','debugbar.hide_empty_tabs','debugbar.except',
'debugbar.storage.enabled','debugbar.storage.open','debugbar.storage.driver','debugbar.storage.path','debugbar.storage.connection',
'debugbar.storage.provider','debugbar.storage.hostname','debugbar.storage.port','debugbar.editor','debugbar.remote_sites_path',
'debugbar.local_sites_path','debugbar.include_vendors','debugbar.capture_ajax','debugbar.add_ajax_timing','debugbar.ajax_handler_auto_show',
'debugbar.ajax_handler_enable_tab','debugbar.defer_datasets','debugbar.error_handler','debugbar.clockwork','debugbar.collectors.phpinfo',
'debugbar.collectors.messages','debugbar.collectors.time','debugbar.collectors.memory','debugbar.collectors.exceptions','debugbar.collectors.log',
'debugbar.collectors.db','debugbar.collectors.views','debugbar.collectors.route','debugbar.collectors.auth','debugbar.collectors.gate',
'debugbar.collectors.session','debugbar.collectors.symfony_request','debugbar.collectors.mail','debugbar.collectors.laravel','debugbar.collectors.events',
'debugbar.collectors.default_request','debugbar.collectors.logs','debugbar.collectors.files','debugbar.collectors.config','debugbar.collectors.cache',
'debugbar.collectors.models','debugbar.collectors.livewire','debugbar.collectors.jobs','debugbar.collectors.pennant','debugbar.options.time.memory_usage',
'debugbar.options.messages.trace','debugbar.options.messages.capture_dumps','debugbar.options.memory.reset_peak','debugbar.options.memory.with_baseline','debugbar.options.memory.precision',
'debugbar.options.auth.show_name','debugbar.options.auth.show_guards','debugbar.options.db.with_params','debugbar.options.db.exclude_paths','debugbar.options.db.backtrace',
'debugbar.options.db.backtrace_exclude_paths','debugbar.options.db.timeline','debugbar.options.db.duration_background','debugbar.options.db.explain.enabled','debugbar.options.db.hints',
'debugbar.options.db.show_copy','debugbar.options.db.slow_threshold','debugbar.options.db.memory_usage','debugbar.options.db.soft_limit','debugbar.options.db.hard_limit',
'debugbar.options.mail.timeline','debugbar.options.mail.show_body','debugbar.options.views.timeline','debugbar.options.views.data','debugbar.options.views.group',
'debugbar.options.views.exclude_paths','debugbar.options.route.label','debugbar.options.session.hiddens','debugbar.options.symfony_request.label','debugbar.options.symfony_request.hiddens',
'debugbar.options.events.data','debugbar.options.logs.file','debugbar.options.cache.values','debugbar.inject','debugbar.route_prefix',
'debugbar.route_middleware','debugbar.route_domain','debugbar.theme','debugbar.debug_backtrace_limit','blade-heroicons.prefix',
'blade-heroicons.fallback','blade-heroicons.class','blade-heroicons.attributes','blade-icons.sets','blade-icons.class',
'blade-icons.attributes','blade-icons.fallback','blade-icons.components.disabled','blade-icons.components.default','inertia.ssr.enabled',
'inertia.ssr.url','inertia.testing.ensure_pages_exist','inertia.testing.page_paths','inertia.testing.page_extensions','inertia.history.encrypt',
'flare.key','flare.flare_middleware','flare.flare_middleware.Spatie\\LaravelIgnition\\FlareMiddleware\\AddLogs.maximum_number_of_collected_logs','flare.flare_middleware.Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries.maximum_number_of_collected_queries','flare.flare_middleware.Spatie\\LaravelIgnition\\FlareMiddleware\\AddQueries.report_query_bindings',
'flare.flare_middleware.Spatie\\LaravelIgnition\\FlareMiddleware\\AddJobs.max_chained_job_reporting_depth','flare.flare_middleware.Spatie\\FlareClient\\FlareMiddleware\\CensorRequestBodyFields.censor_fields','flare.flare_middleware.Spatie\\FlareClient\\FlareMiddleware\\CensorRequestHeaders.headers','flare.send_logs_as_events','ignition.editor',
'ignition.theme','ignition.enable_share_button','ignition.register_commands','ignition.solution_providers','ignition.ignored_solution_providers',
'ignition.enable_runnable_solutions','ignition.remote_sites_path','ignition.local_sites_path','ignition.housekeeping_endpoint_prefix','ignition.settings_file_path',
'ignition.recorders','ignition.open_ai_key','ignition.with_stack_frame_arguments','ignition.argument_reducers','tinker.commands',
'tinker.alias','tinker.dont_alias',);
        registerArgumentsSet('middleware', 
'web','api','auth','auth.basic','auth.session',
'cache.headers','can','guest','password.confirm','precognitive',
'signed','throttle','verified','abilities','ability',
'metrics.access',);
        registerArgumentsSet('routes', 
'debugbar.openhandler','debugbar.clockwork','debugbar.telescope','debugbar.assets.css','debugbar.assets.js',
'debugbar.cache.delete','debugbar.queries.explain','filament.exports.download','filament.imports.failed-rows.download','filament.app.auth.logout',
'filament.app.auth.profile','filament.app.auth.email-verification.prompt','filament.app.auth.email-verification.verify','filament.app.pages.dashboard','filament.app.resources.articles.index',
'filament.app.resources.articles.create','filament.app.resources.articles.edit','filament.app.resources.artist-requests.index','filament.app.resources.artist-requests.view','filament.app.resources.channels.index',
'filament.app.resources.channels.create','filament.app.resources.channels.edit','filament.app.resources.comments.index','filament.app.resources.competitions.index','filament.app.resources.competitions.create',
'filament.app.resources.competitions.edit','filament.app.resources.competitions.view','filament.app.resources.files.index','filament.app.resources.genres.index','filament.app.resources.genres.create',
'filament.app.resources.genres.edit','filament.app.resources.legal-documents.index','filament.app.resources.legal-documents.create','filament.app.resources.legal-documents.view','filament.app.resources.legal-documents.edit',
'filament.app.resources.subscriptions.index','filament.app.resources.subscriptions.create','filament.app.resources.subscriptions.view','filament.app.resources.subscriptions.edit','filament.app.resources.user-agreements.index',
'filament.app.resources.user-agreements.create','filament.app.resources.user-agreements.view','filament.app.resources.user-agreements.edit','filament.app.resources.users.index','filament.app.resources.users.create',
'filament.app.resources.users.edit','filament.app.resources.votes.index','filament.app.resources.votes.create','filament.app.resources.votes.view','filament.app.resources.votes.edit',
'filament.app.resources.vote-transactions.index','filament.app.resources.vote-transactions.create','filament.app.resources.vote-transactions.view','filament.app.resources.vote-transactions.edit','login.store',
'logout','password.email','password.update','register.store','verification.verify',
'verification.send','user-profile-information.update','user-password.update','password.confirmation','password.confirm.store',
'two-factor.login.store','two-factor.enable','two-factor.confirm','two-factor.disable','two-factor.qr-code',
'two-factor.secret-key','two-factor.recovery-codes','horizon.stats.index','horizon.workload.index',
'horizon.masters.index','horizon.monitoring.index','horizon.monitoring.store','horizon.monitoring-tag.paginate','horizon.monitoring-tag.destroy',
'horizon.jobs-metrics.index','horizon.jobs-metrics.show','horizon.queues-metrics.index','horizon.queues-metrics.show','horizon.jobs-batches.index',
'horizon.jobs-batches.show','horizon.jobs-batches.retry','horizon.pending-jobs.index','horizon.completed-jobs.index','horizon.silenced-jobs.index',
'horizon.failed-jobs.index','horizon.failed-jobs.show','horizon.retry-jobs.show','horizon.jobs.show','horizon.index',
'pulse','sanctum.csrf-cookie',
'telescope','livewire.update','livewire.upload-file',
'livewire.preview-file','ignition.healthCheck','ignition.executeSolution','ignition.updateConfig','articles.','articles.','impersonate','impersonate.leave',
'metrics','audio','hls','player.next-dj-track',
'password.reset','socialite.redirect','socialite.callback','login','app.discover',
'app.favorites','app.favorites.articles','app.search','app.search.genres.show','app.search.genres.articles',
'app.artists.show','app.artists.top-charts','app.articles.view','app.channels.show','app.channels.articles',
'app.channels.download','app.channels.view','app.playlists.show','app.playlists.articles','app.plays.article',
'app.followings.toggle','app.like.channel','app.like.article','app.like.playlist','app.notifications.clear',
'app.notifications.destroy','app.comments.update','app.comments.delete','app.comments.article','app.comments.article.create',
'app.comments.channel','app.comments.channel.create','app.settings.account','app.settings.account.image-profile','app.settings.artist-request',
'app.voting.index','app.voting.leaderboard','app.voting.artist','app.voting.entry.form','app.voting.entry.submit',
'app.voting.vote','app.voting.share','app.voting.payment.mock','app.voting.payment.mock.process','app.voting.payment.callback',
'app.voting.subscription.checkout','app.legal.documents','app.legal.document','app.legal.view',
'storage.local','scramble.docs.ui','scramble.docs.document',);
        registerArgumentsSet('views', 
'app','app.payment.mock','components.theme-loader','filament.pages.profile','pulse::dashboard',
'tables.columns.audio-player','vendor.pulse.dashboard','7224ff0b1fa42156c352216deff15b56::card','7224ff0b1fa42156c352216deff15b56::card-header','7224ff0b1fa42156c352216deff15b56::http-method-badge',
'7224ff0b1fa42156c352216deff15b56::icons.arrow-trending-up','7224ff0b1fa42156c352216deff15b56::icons.arrows-left-right','7224ff0b1fa42156c352216deff15b56::icons.bug-ant','7224ff0b1fa42156c352216deff15b56::icons.circle-stack','7224ff0b1fa42156c352216deff15b56::icons.clipboard',
'7224ff0b1fa42156c352216deff15b56::icons.clock','7224ff0b1fa42156c352216deff15b56::icons.cloud-arrow-up','7224ff0b1fa42156c352216deff15b56::icons.command-line','7224ff0b1fa42156c352216deff15b56::icons.computer-desktop','7224ff0b1fa42156c352216deff15b56::icons.cursor-arrow-rays',
'7224ff0b1fa42156c352216deff15b56::icons.ellipsis-horizontal','7224ff0b1fa42156c352216deff15b56::icons.information-circle','7224ff0b1fa42156c352216deff15b56::icons.moon','7224ff0b1fa42156c352216deff15b56::icons.no-pulse','7224ff0b1fa42156c352216deff15b56::icons.queue-list',
'7224ff0b1fa42156c352216deff15b56::icons.rocket-launch','7224ff0b1fa42156c352216deff15b56::icons.scale','7224ff0b1fa42156c352216deff15b56::icons.server','7224ff0b1fa42156c352216deff15b56::icons.signal-slash','7224ff0b1fa42156c352216deff15b56::icons.sparkles',
'7224ff0b1fa42156c352216deff15b56::icons.sun','7224ff0b1fa42156c352216deff15b56::no-results','7224ff0b1fa42156c352216deff15b56::placeholder','7224ff0b1fa42156c352216deff15b56::pulse','7224ff0b1fa42156c352216deff15b56::scroll',
'7224ff0b1fa42156c352216deff15b56::select','7224ff0b1fa42156c352216deff15b56::servers-placeholder','7224ff0b1fa42156c352216deff15b56::table','7224ff0b1fa42156c352216deff15b56::td','7224ff0b1fa42156c352216deff15b56::th',
'7224ff0b1fa42156c352216deff15b56::thead','7224ff0b1fa42156c352216deff15b56::theme-switcher','7224ff0b1fa42156c352216deff15b56::user-card','filament-actions::badge-action','filament-actions::badge-group',
'filament-actions::button-action','filament-actions::button-group','filament-actions::components.action','filament-actions::components.actions','filament-actions::components.group',
'filament-actions::components.modals','filament-actions::grouped-action','filament-actions::grouped-group','filament-actions::icon-button-action','filament-actions::icon-button-group',
'filament-actions::link-action','filament-actions::link-group','filament-actions::select-action','filament-forms::component-container','filament-forms::components.actions',
'filament-forms::components.actions.action-container','filament-forms::components.builder','filament-forms::components.builder.block-picker','filament-forms::components.checkbox','filament-forms::components.checkbox-list',
'filament-forms::components.color-picker','filament-forms::components.date-time-picker','filament-forms::components.field-wrapper.error-message','filament-forms::components.field-wrapper.helper-text','filament-forms::components.field-wrapper.hint',
'filament-forms::components.field-wrapper.index','filament-forms::components.field-wrapper.label','filament-forms::components.fieldset','filament-forms::components.file-upload','filament-forms::components.grid',
'filament-forms::components.group','filament-forms::components.hidden','filament-forms::components.key-value','filament-forms::components.livewire','filament-forms::components.markdown-editor',
'filament-forms::components.placeholder','filament-forms::components.radio','filament-forms::components.repeater.index','filament-forms::components.repeater.simple','filament-forms::components.rich-editor',
'filament-forms::components.rich-editor.toolbar.button','filament-forms::components.rich-editor.toolbar.group','filament-forms::components.section','filament-forms::components.select','filament-forms::components.split',
'filament-forms::components.tabs','filament-forms::components.tabs.tab','filament-forms::components.tags-input','filament-forms::components.text-input','filament-forms::components.textarea',
'filament-forms::components.toggle','filament-forms::components.toggle-buttons.grouped','filament-forms::components.toggle-buttons.index','filament-forms::components.wizard','filament-forms::components.wizard.step',
'filament-infolists::component-container','filament-infolists::components.actions','filament-infolists::components.actions.action-container','filament-infolists::components.affixes','filament-infolists::components.color-entry',
'filament-infolists::components.entries.placeholder','filament-infolists::components.entry-wrapper.helper-text','filament-infolists::components.entry-wrapper.hint','filament-infolists::components.entry-wrapper.index','filament-infolists::components.entry-wrapper.label',
'filament-infolists::components.fieldset','filament-infolists::components.grid','filament-infolists::components.group','filament-infolists::components.icon-entry','filament-infolists::components.image-entry',
'filament-infolists::components.key-value-entry','filament-infolists::components.livewire','filament-infolists::components.repeatable-entry','filament-infolists::components.section','filament-infolists::components.split',
'filament-infolists::components.tabs','filament-infolists::components.tabs.tab','filament-infolists::components.text-entry','filament-media-action::actions.media-modal-content','filament-notifications::components.actions',
'filament-notifications::components.body','filament-notifications::components.close-button','filament-notifications::components.database.echo','filament-notifications::components.database.modal.actions','filament-notifications::components.database.modal.heading',
'filament-notifications::components.database.modal.index','filament-notifications::components.database.trigger','filament-notifications::components.date','filament-notifications::components.echo','filament-notifications::components.icon',
'filament-notifications::components.notification','filament-notifications::components.title','filament-notifications::database-notifications','filament-notifications::notification','filament-notifications::notifications',
'filament-panels::components.avatar.tenant','filament-panels::components.avatar.user','filament-panels::components.form.actions','filament-panels::components.form.index','filament-panels::components.global-search.actions',
'filament-panels::components.global-search.field','filament-panels::components.global-search.index','filament-panels::components.global-search.no-results-message','filament-panels::components.global-search.result','filament-panels::components.global-search.result-group',
'filament-panels::components.global-search.results-container','filament-panels::components.header.index','filament-panels::components.header.simple','filament-panels::components.layout.base','filament-panels::components.layout.index',
'filament-panels::components.layout.simple','filament-panels::components.logo','filament-panels::components.page.index','filament-panels::components.page.simple','filament-panels::components.page.sub-navigation.select',
'filament-panels::components.page.sub-navigation.sidebar','filament-panels::components.page.sub-navigation.tabs','filament-panels::components.page.unsaved-data-changes-alert','filament-panels::components.resources.relation-managers','filament-panels::components.resources.tabs',
'filament-panels::components.sidebar.group','filament-panels::components.sidebar.index','filament-panels::components.sidebar.item','filament-panels::components.tenant-menu','filament-panels::components.theme-switcher.button',
'filament-panels::components.theme-switcher.index','filament-panels::components.topbar.database-notifications-trigger','filament-panels::components.topbar.index','filament-panels::components.topbar.item','filament-panels::components.unsaved-action-changes-alert',
'filament-panels::components.user-menu','filament-panels::pages.auth.edit-profile','filament-panels::pages.auth.email-verification.email-verification-prompt','filament-panels::pages.auth.login','filament-panels::pages.auth.password-reset.request-password-reset',
'filament-panels::pages.auth.password-reset.reset-password','filament-panels::pages.auth.register','filament-panels::pages.dashboard','filament-panels::pages.tenancy.edit-tenant-profile','filament-panels::pages.tenancy.register-tenant',
'filament-panels::resources.pages.create-record','filament-panels::resources.pages.edit-record','filament-panels::resources.pages.list-records','filament-panels::resources.pages.manage-related-records','filament-panels::resources.pages.view-record',
'filament-panels::resources.relation-manager','filament-panels::widgets.account-widget','filament-panels::widgets.filament-info-widget','filament-tables::columns.checkbox-column','filament-tables::columns.color-column',
'filament-tables::columns.icon-column','filament-tables::columns.image-column','filament-tables::columns.layout.grid','filament-tables::columns.layout.panel','filament-tables::columns.layout.split',
'filament-tables::columns.layout.stack','filament-tables::columns.select-column','filament-tables::columns.summaries.icon-count','filament-tables::columns.summaries.range','filament-tables::columns.summaries.text',
'filament-tables::columns.summaries.values','filament-tables::columns.text-column','filament-tables::columns.text-input-column','filament-tables::columns.toggle-column','filament-tables::components.actions',
'filament-tables::components.actions.cell','filament-tables::components.cell','filament-tables::components.column-toggle.dropdown','filament-tables::components.columns.column','filament-tables::components.columns.layout',
'filament-tables::components.columns.placeholder','filament-tables::components.container','filament-tables::components.empty-state.description','filament-tables::components.empty-state.heading','filament-tables::components.empty-state.index',
'filament-tables::components.filters.dialog','filament-tables::components.filters.index','filament-tables::components.filters.indicators','filament-tables::components.group.header','filament-tables::components.groups',
'filament-tables::components.header','filament-tables::components.header-cell','filament-tables::components.reorder.cell','filament-tables::components.reorder.handle','filament-tables::components.reorder.indicator',
'filament-tables::components.row','filament-tables::components.search-field','filament-tables::components.selection.cell','filament-tables::components.selection.checkbox','filament-tables::components.selection.group-cell',
'filament-tables::components.selection.group-checkbox','filament-tables::components.selection.indicator','filament-tables::components.summary.header-cell','filament-tables::components.summary.index','filament-tables::components.summary.row',
'filament-tables::components.table','filament-tables::index','filament-widgets::chart-widget','filament-widgets::components.widget','filament-widgets::components.widgets',
'filament-widgets::stats-overview-widget','filament-widgets::stats-overview-widget.stat','filament-widgets::table-widget','filament::assets','filament::components.actions',
'filament::components.avatar','filament::components.badge','filament::components.breadcrumbs','filament::components.button.group','filament::components.button.index',
'filament::components.card','filament::components.dropdown.header','filament::components.dropdown.index','filament::components.dropdown.list.index','filament::components.dropdown.list.item',
'filament::components.fieldset','filament::components.grid.column','filament::components.grid.index','filament::components.icon','filament::components.icon-button',
'filament::components.input.checkbox','filament::components.input.index','filament::components.input.radio','filament::components.input.select','filament::components.input.wrapper',
'filament::components.link','filament::components.loading-indicator','filament::components.loading-section','filament::components.modal.description','filament::components.modal.heading',
'filament::components.modal.index','filament::components.pagination.index','filament::components.pagination.item','filament::components.section.description','filament::components.section.heading',
'filament::components.section.index','filament::components.tabs.index','filament::components.tabs.item','horizon::layout','laravel-exceptions-renderer::components.card',
'laravel-exceptions-renderer::components.context','laravel-exceptions-renderer::components.editor','laravel-exceptions-renderer::components.header','laravel-exceptions-renderer::components.icons.chevron-down','laravel-exceptions-renderer::components.icons.chevron-up',
'laravel-exceptions-renderer::components.icons.computer-desktop','laravel-exceptions-renderer::components.icons.moon','laravel-exceptions-renderer::components.icons.sun','laravel-exceptions-renderer::components.layout','laravel-exceptions-renderer::components.navigation',
'laravel-exceptions-renderer::components.theme-switcher','laravel-exceptions-renderer::components.trace','laravel-exceptions-renderer::components.trace-and-editor','laravel-exceptions-renderer::show','laravel-exceptions::401',
'laravel-exceptions::402','laravel-exceptions::403','laravel-exceptions::404','laravel-exceptions::419','laravel-exceptions::429',
'laravel-exceptions::500','laravel-exceptions::503','laravel-exceptions::layout','laravel-exceptions::minimal','livewire::bootstrap',
'livewire::simple-bootstrap','livewire::simple-tailwind','livewire::tailwind','notifications::email','pagination::bootstrap-4',
'pagination::bootstrap-5','pagination::default','pagination::semantic-ui','pagination::simple-bootstrap-4','pagination::simple-bootstrap-5',
'pagination::simple-default','pagination::simple-tailwind','pagination::tailwind','pulse::components.card','pulse::components.card-header',
'pulse::components.http-method-badge','pulse::components.icons.arrow-trending-up','pulse::components.icons.arrows-left-right','pulse::components.icons.bug-ant','pulse::components.icons.circle-stack',
'pulse::components.icons.clipboard','pulse::components.icons.clock','pulse::components.icons.cloud-arrow-up','pulse::components.icons.command-line','pulse::components.icons.computer-desktop',
'pulse::components.icons.cursor-arrow-rays','pulse::components.icons.ellipsis-horizontal','pulse::components.icons.information-circle','pulse::components.icons.moon','pulse::components.icons.no-pulse',
'pulse::components.icons.queue-list','pulse::components.icons.rocket-launch','pulse::components.icons.scale','pulse::components.icons.server','pulse::components.icons.signal-slash',
'pulse::components.icons.sparkles','pulse::components.icons.sun','pulse::components.no-results','pulse::components.placeholder','pulse::components.pulse',
'pulse::components.scroll','pulse::components.select','pulse::components.servers-placeholder','pulse::components.table','pulse::components.td',
'pulse::components.th','pulse::components.thead','pulse::components.theme-switcher','pulse::components.user-card','pulse::dashboard',
'pulse::livewire.cache','pulse::livewire.exceptions','pulse::livewire.period-selector','pulse::livewire.queues','pulse::livewire.servers',
'pulse::livewire.slow-jobs','pulse::livewire.slow-outgoing-requests','pulse::livewire.slow-queries','pulse::livewire.slow-requests','pulse::livewire.usage',
'reverb::components.icons.reverb','reverb::livewire.connections','reverb::livewire.messages','scramble::docs','telescope::layout',);
        registerArgumentsSet('translations', 
'auth.failed','auth.password','auth.throttle','pagination.previous','pagination.next',
'passwords.reset','passwords.sent','passwords.throttled','passwords.token','passwords.user',
'validation.accepted','validation.accepted_if','validation.active_url','validation.after','validation.after_or_equal',
'validation.alpha','validation.alpha_dash','validation.alpha_num','validation.any_of','validation.array',
'validation.ascii','validation.before','validation.before_or_equal','validation.between.array','validation.between.file',
'validation.between.numeric','validation.between.string','validation.boolean','validation.can','validation.confirmed',
'validation.contains','validation.current_password','validation.date','validation.date_equals','validation.date_format',
'validation.decimal','validation.declined','validation.declined_if','validation.different','validation.digits',
'validation.digits_between','validation.dimensions','validation.distinct','validation.doesnt_end_with','validation.doesnt_start_with',
'validation.email','validation.ends_with','validation.enum','validation.exists','validation.extensions',
'validation.file','validation.filled','validation.gt.array','validation.gt.file','validation.gt.numeric',
'validation.gt.string','validation.gte.array','validation.gte.file','validation.gte.numeric','validation.gte.string',
'validation.hex_color','validation.image','validation.in','validation.in_array','validation.in_array_keys',
'validation.integer','validation.ip','validation.ipv4','validation.ipv6','validation.json',
'validation.list','validation.lowercase','validation.lt.array','validation.lt.file','validation.lt.numeric',
'validation.lt.string','validation.lte.array','validation.lte.file','validation.lte.numeric','validation.lte.string',
'validation.mac_address','validation.max.array','validation.max.file','validation.max.numeric','validation.max.string',
'validation.max_digits','validation.mimes','validation.mimetypes','validation.min.array','validation.min.file',
'validation.min.numeric','validation.min.string','validation.min_digits','validation.missing','validation.missing_if',
'validation.missing_unless','validation.missing_with','validation.missing_with_all','validation.multiple_of','validation.not_in',
'validation.not_regex','validation.numeric','validation.password.letters','validation.password.mixed','validation.password.numbers',
'validation.password.symbols','validation.password.uncompromised','validation.present','validation.present_if','validation.present_unless',
'validation.present_with','validation.present_with_all','validation.prohibited','validation.prohibited_if','validation.prohibited_if_accepted',
'validation.prohibited_if_declined','validation.prohibited_unless','validation.prohibits','validation.regex','validation.required',
'validation.required_array_keys','validation.required_if','validation.required_if_accepted','validation.required_if_declined','validation.required_unless',
'validation.required_with','validation.required_with_all','validation.required_without','validation.required_without_all','validation.same',
'validation.size.array','validation.size.file','validation.size.numeric','validation.size.string','validation.starts_with',
'validation.string','validation.timezone','validation.unique','validation.uploaded','validation.uppercase',
'validation.url','validation.ulid','validation.uuid','validation.custom.attribute-name.rule-name','notifications.0',
'filament-actions::associate.single.label','filament-actions::associate.single.modal.heading','filament-actions::associate.single.modal.fields.record_id.label','filament-actions::associate.single.modal.actions.associate.label','filament-actions::associate.single.modal.actions.associate_another.label',
'filament-actions::associate.single.notifications.associated.title','filament-actions::attach.single.label','filament-actions::attach.single.modal.heading','filament-actions::attach.single.modal.fields.record_id.label','filament-actions::attach.single.modal.actions.attach.label',
'filament-actions::attach.single.modal.actions.attach_another.label','filament-actions::attach.single.notifications.attached.title','filament-actions::create.single.label','filament-actions::create.single.modal.heading','filament-actions::create.single.modal.actions.create.label',
'filament-actions::create.single.modal.actions.create_another.label','filament-actions::create.single.notifications.created.title','filament-actions::delete.single.label','filament-actions::delete.single.modal.heading','filament-actions::delete.single.modal.actions.delete.label',
'filament-actions::delete.single.notifications.deleted.title','filament-actions::delete.multiple.label','filament-actions::delete.multiple.modal.heading','filament-actions::delete.multiple.modal.actions.delete.label','filament-actions::delete.multiple.notifications.deleted.title',
'filament-actions::detach.single.label','filament-actions::detach.single.modal.heading','filament-actions::detach.single.modal.actions.detach.label','filament-actions::detach.single.notifications.detached.title','filament-actions::detach.multiple.label',
'filament-actions::detach.multiple.modal.heading','filament-actions::detach.multiple.modal.actions.detach.label','filament-actions::detach.multiple.notifications.detached.title','filament-actions::dissociate.single.label','filament-actions::dissociate.single.modal.heading',
'filament-actions::dissociate.single.modal.actions.dissociate.label','filament-actions::dissociate.single.notifications.dissociated.title','filament-actions::dissociate.multiple.label','filament-actions::dissociate.multiple.modal.heading','filament-actions::dissociate.multiple.modal.actions.dissociate.label',
'filament-actions::dissociate.multiple.notifications.dissociated.title','filament-actions::edit.single.label','filament-actions::edit.single.modal.heading','filament-actions::edit.single.modal.actions.save.label','filament-actions::edit.single.notifications.saved.title',
'filament-actions::export.label','filament-actions::export.modal.heading','filament-actions::export.modal.form.columns.label','filament-actions::export.modal.form.columns.form.is_enabled.label','filament-actions::export.modal.form.columns.form.label.label',
'filament-actions::export.modal.actions.export.label','filament-actions::export.notifications.completed.title','filament-actions::export.notifications.completed.actions.download_csv.label','filament-actions::export.notifications.completed.actions.download_xlsx.label','filament-actions::export.notifications.max_rows.title',
'filament-actions::export.notifications.max_rows.body','filament-actions::export.notifications.started.title','filament-actions::export.notifications.started.body','filament-actions::export.file_name','filament-actions::force-delete.single.label',
'filament-actions::force-delete.single.modal.heading','filament-actions::force-delete.single.modal.actions.delete.label','filament-actions::force-delete.single.notifications.deleted.title','filament-actions::force-delete.multiple.label','filament-actions::force-delete.multiple.modal.heading',
'filament-actions::force-delete.multiple.modal.actions.delete.label','filament-actions::force-delete.multiple.notifications.deleted.title','filament-actions::group.trigger.label','filament-actions::import.label','filament-actions::import.modal.heading',
'filament-actions::import.modal.form.file.label','filament-actions::import.modal.form.file.placeholder','filament-actions::import.modal.form.file.rules.duplicate_columns','filament-actions::import.modal.form.columns.label','filament-actions::import.modal.form.columns.placeholder',
'filament-actions::import.modal.actions.download_example.label','filament-actions::import.modal.actions.import.label','filament-actions::import.notifications.completed.title','filament-actions::import.notifications.completed.actions.download_failed_rows_csv.label','filament-actions::import.notifications.max_rows.title',
'filament-actions::import.notifications.max_rows.body','filament-actions::import.notifications.started.title','filament-actions::import.notifications.started.body','filament-actions::import.example_csv.file_name','filament-actions::import.failure_csv.file_name',
'filament-actions::import.failure_csv.error_header','filament-actions::import.failure_csv.system_error','filament-actions::import.failure_csv.column_mapping_required_for_new_record','filament-actions::modal.confirmation','filament-actions::modal.actions.cancel.label',
'filament-actions::modal.actions.confirm.label','filament-actions::modal.actions.submit.label','filament-actions::replicate.single.label','filament-actions::replicate.single.modal.heading','filament-actions::replicate.single.modal.actions.replicate.label',
'filament-actions::replicate.single.notifications.replicated.title','filament-actions::restore.single.label','filament-actions::restore.single.modal.heading','filament-actions::restore.single.modal.actions.restore.label','filament-actions::restore.single.notifications.restored.title',
'filament-actions::restore.multiple.label','filament-actions::restore.multiple.modal.heading','filament-actions::restore.multiple.modal.actions.restore.label','filament-actions::restore.multiple.notifications.restored.title','filament-actions::view.single.label',
'filament-actions::view.single.modal.heading','filament-actions::view.single.modal.actions.close.label','filament-actions::global-search.0','filament-actions::layout.0','filament-actions::edit-profile.0',
'filament-actions::email-verification-prompt.0','filament-actions::login.0','filament-actions::request-password-reset.0','filament-actions::reset-password.0','filament-actions::register.0',
'filament-actions::dashboard.0','filament-actions::edit-tenant-profile.0','filament-actions::create-record.0','filament-actions::edit-record.0','filament-actions::list-records.0',
'filament-actions::view-record.0','filament-actions::account-widget.0','filament-actions::filament-info-widget.0','filament-panels::global-search.field.label','filament-panels::global-search.field.placeholder',
'filament-panels::global-search.no_results_message','filament-panels::layout.direction','filament-panels::layout.actions.billing.label','filament-panels::layout.actions.logout.label','filament-panels::layout.actions.open_database_notifications.label',
'filament-panels::layout.actions.open_user_menu.label','filament-panels::layout.actions.sidebar.collapse.label','filament-panels::layout.actions.sidebar.expand.label','filament-panels::layout.actions.theme_switcher.dark.label','filament-panels::layout.actions.theme_switcher.light.label',
'filament-panels::layout.actions.theme_switcher.system.label','filament-panels::layout.avatar.alt','filament-panels::layout.logo.alt','filament-panels::edit-profile.0','filament-panels::email-verification-prompt.0',
'filament-panels::login.0','filament-panels::request-password-reset.0','filament-panels::reset-password.0','filament-panels::register.0','filament-panels::dashboard.0',
'filament-panels::edit-tenant-profile.0','filament-panels::create-record.0','filament-panels::edit-record.0','filament-panels::list-records.0','filament-panels::view-record.0',
'filament-panels::unsaved-changes-alert.body','filament-panels::account-widget.0','filament-panels::filament-info-widget.0','filament-forms::components.builder.actions.clone.label','filament-forms::components.builder.actions.add.label',
'filament-forms::components.builder.actions.add.modal.heading','filament-forms::components.builder.actions.add.modal.actions.add.label','filament-forms::components.builder.actions.add_between.label','filament-forms::components.builder.actions.add_between.modal.heading','filament-forms::components.builder.actions.add_between.modal.actions.add.label',
'filament-forms::components.builder.actions.delete.label','filament-forms::components.builder.actions.edit.label','filament-forms::components.builder.actions.edit.modal.heading','filament-forms::components.builder.actions.edit.modal.actions.save.label','filament-forms::components.builder.actions.reorder.label',
'filament-forms::components.builder.actions.move_down.label','filament-forms::components.builder.actions.move_up.label','filament-forms::components.builder.actions.collapse.label','filament-forms::components.builder.actions.expand.label','filament-forms::components.builder.actions.collapse_all.label',
'filament-forms::components.builder.actions.expand_all.label','filament-forms::components.checkbox_list.actions.deselect_all.label','filament-forms::components.checkbox_list.actions.select_all.label','filament-forms::components.file_upload.editor.actions.cancel.label','filament-forms::components.file_upload.editor.actions.drag_crop.label',
'filament-forms::components.file_upload.editor.actions.drag_move.label','filament-forms::components.file_upload.editor.actions.flip_horizontal.label','filament-forms::components.file_upload.editor.actions.flip_vertical.label','filament-forms::components.file_upload.editor.actions.move_down.label','filament-forms::components.file_upload.editor.actions.move_left.label',
'filament-forms::components.file_upload.editor.actions.move_right.label','filament-forms::components.file_upload.editor.actions.move_up.label','filament-forms::components.file_upload.editor.actions.reset.label','filament-forms::components.file_upload.editor.actions.rotate_left.label','filament-forms::components.file_upload.editor.actions.rotate_right.label',
'filament-forms::components.file_upload.editor.actions.set_aspect_ratio.label','filament-forms::components.file_upload.editor.actions.save.label','filament-forms::components.file_upload.editor.actions.zoom_100.label','filament-forms::components.file_upload.editor.actions.zoom_in.label','filament-forms::components.file_upload.editor.actions.zoom_out.label',
'filament-forms::components.file_upload.editor.fields.height.label','filament-forms::components.file_upload.editor.fields.height.unit','filament-forms::components.file_upload.editor.fields.rotation.label','filament-forms::components.file_upload.editor.fields.rotation.unit','filament-forms::components.file_upload.editor.fields.width.label',
'filament-forms::components.file_upload.editor.fields.width.unit','filament-forms::components.file_upload.editor.fields.x_position.label','filament-forms::components.file_upload.editor.fields.x_position.unit','filament-forms::components.file_upload.editor.fields.y_position.label','filament-forms::components.file_upload.editor.fields.y_position.unit',
'filament-forms::components.file_upload.editor.aspect_ratios.label','filament-forms::components.file_upload.editor.aspect_ratios.no_fixed.label','filament-forms::components.file_upload.editor.svg.messages.confirmation','filament-forms::components.file_upload.editor.svg.messages.disabled','filament-forms::components.key_value.actions.add.label',
'filament-forms::components.key_value.actions.delete.label','filament-forms::components.key_value.actions.reorder.label','filament-forms::components.key_value.fields.key.label','filament-forms::components.key_value.fields.value.label','filament-forms::components.markdown_editor.toolbar_buttons.attach_files',
'filament-forms::components.markdown_editor.toolbar_buttons.blockquote','filament-forms::components.markdown_editor.toolbar_buttons.bold','filament-forms::components.markdown_editor.toolbar_buttons.bullet_list','filament-forms::components.markdown_editor.toolbar_buttons.code_block','filament-forms::components.markdown_editor.toolbar_buttons.heading',
'filament-forms::components.markdown_editor.toolbar_buttons.italic','filament-forms::components.markdown_editor.toolbar_buttons.link','filament-forms::components.markdown_editor.toolbar_buttons.ordered_list','filament-forms::components.markdown_editor.toolbar_buttons.redo','filament-forms::components.markdown_editor.toolbar_buttons.strike',
'filament-forms::components.markdown_editor.toolbar_buttons.table','filament-forms::components.markdown_editor.toolbar_buttons.undo','filament-forms::components.radio.boolean.true','filament-forms::components.radio.boolean.false','filament-forms::components.repeater.actions.add.label',
'filament-forms::components.repeater.actions.add_between.label','filament-forms::components.repeater.actions.delete.label','filament-forms::components.repeater.actions.clone.label','filament-forms::components.repeater.actions.reorder.label','filament-forms::components.repeater.actions.move_down.label',
'filament-forms::components.repeater.actions.move_up.label','filament-forms::components.repeater.actions.collapse.label','filament-forms::components.repeater.actions.expand.label','filament-forms::components.repeater.actions.collapse_all.label','filament-forms::components.repeater.actions.expand_all.label',
'filament-forms::components.rich_editor.dialogs.link.actions.link','filament-forms::components.rich_editor.dialogs.link.actions.unlink','filament-forms::components.rich_editor.dialogs.link.label','filament-forms::components.rich_editor.dialogs.link.placeholder','filament-forms::components.rich_editor.toolbar_buttons.attach_files',
'filament-forms::components.rich_editor.toolbar_buttons.blockquote','filament-forms::components.rich_editor.toolbar_buttons.bold','filament-forms::components.rich_editor.toolbar_buttons.bullet_list','filament-forms::components.rich_editor.toolbar_buttons.code_block','filament-forms::components.rich_editor.toolbar_buttons.h1',
'filament-forms::components.rich_editor.toolbar_buttons.h2','filament-forms::components.rich_editor.toolbar_buttons.h3','filament-forms::components.rich_editor.toolbar_buttons.italic','filament-forms::components.rich_editor.toolbar_buttons.link','filament-forms::components.rich_editor.toolbar_buttons.ordered_list',
'filament-forms::components.rich_editor.toolbar_buttons.redo','filament-forms::components.rich_editor.toolbar_buttons.strike','filament-forms::components.rich_editor.toolbar_buttons.underline','filament-forms::components.rich_editor.toolbar_buttons.undo','filament-forms::components.select.actions.create_option.label',
'filament-forms::components.select.actions.create_option.modal.heading','filament-forms::components.select.actions.create_option.modal.actions.create.label','filament-forms::components.select.actions.create_option.modal.actions.create_another.label','filament-forms::components.select.actions.edit_option.label','filament-forms::components.select.actions.edit_option.modal.heading',
'filament-forms::components.select.actions.edit_option.modal.actions.save.label','filament-forms::components.select.boolean.true','filament-forms::components.select.boolean.false','filament-forms::components.select.loading_message','filament-forms::components.select.max_items_message',
'filament-forms::components.select.no_search_results_message','filament-forms::components.select.placeholder','filament-forms::components.select.searching_message','filament-forms::components.select.search_prompt','filament-forms::components.tags_input.placeholder',
'filament-forms::components.text_input.actions.hide_password.label','filament-forms::components.text_input.actions.show_password.label','filament-forms::components.toggle_buttons.boolean.true','filament-forms::components.toggle_buttons.boolean.false','filament-forms::components.wizard.actions.previous_step.label',
'filament-forms::components.wizard.actions.next_step.label','filament-forms::validation.distinct.must_be_selected','filament-forms::validation.distinct.only_one_must_be_selected','filament-infolists::components.entries.text.actions.collapse_list','filament-infolists::components.entries.text.actions.expand_list',
'filament-infolists::components.entries.text.more_list_items','filament-infolists::components.entries.key_value.columns.key.label','filament-infolists::components.entries.key_value.columns.value.label','filament-infolists::components.entries.key_value.placeholder','filament-notifications::database.modal.heading',
'filament-notifications::database.modal.actions.clear.label','filament-notifications::database.modal.actions.mark_all_as_read.label','filament-notifications::database.modal.empty.heading','filament-notifications::database.modal.empty.description','filament::button.0',
'filament::copyable.0','filament::modal.0','filament::pagination.0','filament-tables::query-builder.0','filament-tables::table.column_toggle.heading',
'filament-tables::table.columns.actions.label','filament-tables::table.columns.text.actions.collapse_list','filament-tables::table.columns.text.actions.expand_list','filament-tables::table.columns.text.more_list_items','filament-tables::table.fields.bulk_select_page.label',
'filament-tables::table.fields.bulk_select_record.label','filament-tables::table.fields.bulk_select_group.label','filament-tables::table.fields.search.label','filament-tables::table.fields.search.placeholder','filament-tables::table.fields.search.indicator',
'filament-tables::table.summary.heading','filament-tables::table.summary.subheadings.all','filament-tables::table.summary.subheadings.group','filament-tables::table.summary.subheadings.page','filament-tables::table.summary.summarizers.average.label',
'filament-tables::table.summary.summarizers.count.label','filament-tables::table.summary.summarizers.sum.label','filament-tables::table.actions.disable_reordering.label','filament-tables::table.actions.enable_reordering.label','filament-tables::table.actions.filter.label',
'filament-tables::table.actions.group.label','filament-tables::table.actions.open_bulk_actions.label','filament-tables::table.actions.toggle_columns.label','filament-tables::table.empty.heading','filament-tables::table.empty.description',
'filament-tables::table.filters.actions.apply.label','filament-tables::table.filters.actions.remove.label','filament-tables::table.filters.actions.remove_all.label','filament-tables::table.filters.actions.remove_all.tooltip','filament-tables::table.filters.actions.reset.label',
'filament-tables::table.filters.heading','filament-tables::table.filters.indicator','filament-tables::table.filters.multi_select.placeholder','filament-tables::table.filters.select.placeholder','filament-tables::table.filters.trashed.label',
'filament-tables::table.filters.trashed.only_trashed','filament-tables::table.filters.trashed.with_trashed','filament-tables::table.filters.trashed.without_trashed','filament-tables::table.grouping.fields.group.label','filament-tables::table.grouping.fields.group.placeholder',
'filament-tables::table.grouping.fields.direction.label','filament-tables::table.grouping.fields.direction.options.asc','filament-tables::table.grouping.fields.direction.options.desc','filament-tables::table.reorder_indicator','filament-tables::table.selection_indicator.selected_count',
'filament-tables::table.selection_indicator.actions.select_all.label','filament-tables::table.selection_indicator.actions.deselect_all.label','filament-tables::table.sorting.fields.column.label','filament-tables::table.sorting.fields.direction.label','filament-tables::table.sorting.fields.direction.options.asc',
'filament-tables::table.sorting.fields.direction.options.desc','filament-media-action::media-action.loading','filament-media-action::media-action.unsupported-media-type','backup::notifications.exception_message','backup::notifications.exception_trace',
'backup::notifications.exception_message_title','backup::notifications.exception_trace_title','backup::notifications.backup_failed_subject','backup::notifications.backup_failed_body','backup::notifications.backup_successful_subject',
'backup::notifications.backup_successful_subject_title','backup::notifications.backup_successful_body','backup::notifications.cleanup_failed_subject','backup::notifications.cleanup_failed_body','backup::notifications.cleanup_successful_subject',
'backup::notifications.cleanup_successful_subject_title','backup::notifications.cleanup_successful_body','backup::notifications.healthy_backup_found_subject','backup::notifications.healthy_backup_found_subject_title','backup::notifications.healthy_backup_found_body',
'backup::notifications.unhealthy_backup_found_subject','backup::notifications.unhealthy_backup_found_subject_title','backup::notifications.unhealthy_backup_found_body','backup::notifications.unhealthy_backup_found_not_reachable','backup::notifications.unhealthy_backup_found_empty',
'backup::notifications.unhealthy_backup_found_old','backup::notifications.unhealthy_backup_found_unknown','backup::notifications.unhealthy_backup_found_full','backup::notifications.no_backups_info','backup::notifications.application_name',
'backup::notifications.backup_name','backup::notifications.disk','backup::notifications.newest_backup_size','backup::notifications.number_of_backups','backup::notifications.total_storage_used',
'backup::notifications.newest_backup_date','backup::notifications.oldest_backup_date',);
        registerArgumentsSet('env', 
'APP_NAME','APP_ENV','APP_KEY','APP_DEBUG','APP_URL',
'APP_LOCALE','APP_FALLBACK_LOCALE','APP_FAKER_LOCALE','APP_MAINTENANCE_DRIVER','OCTANE_SERVER',
'OCTANE_HTTPS','LOG_CHANNEL','LOG_DEPRECATIONS_CHANNEL','LOG_LEVEL','DB_CONNECTION',
'DB_HOST','DB_PORT','DB_DATABASE','DB_USERNAME','DB_PASSWORD',
'CACHE_DRIVER','FILESYSTEM_DISK','BROADCAST_DRIVER','BROADCAST_CONNECTION','QUEUE_CONNECTION',
'SESSION_DRIVER','SESSION_LIFETIME','SESSION_ENCRYPT','SESSION_PATH','SESSION_DOMAIN',
'MEMCACHED_HOST','REDIS_CLIENT','REDIS_HOST','REDIS_PASSWORD','REDIS_PORT',
'MAIL_MAILER','MAIL_HOST','MAIL_PORT','MAIL_USERNAME','MAIL_PASSWORD',
'MAIL_ENCRYPTION','MAIL_FROM_ADDRESS','MAIL_FROM_NAME','SCOUT_DRIVER','PUSHER_APP_ID',
'PUSHER_APP_KEY','PUSHER_APP_SECRET','PUSHER_HOST','PUSHER_PORT','PUSHER_SCHEME',
'PUSHER_APP_CLUSTER','VITE_APP_NAME','VITE_PUSHER_APP_KEY','VITE_PUSHER_HOST','VITE_PUSHER_PORT',
'VITE_PUSHER_SCHEME','VITE_PUSHER_APP_CLUSTER','GOOGLE_CLIENT_ID','GOOGLE_CLIENT_SECRET','GOOGLE_REDIRECT',
'MEILISEARCH_HOST','MEILISEARCH_KEY','MEILI_MASTER_KEY','SERVICE_MAEG_URL','TELEGRAM_BOT_TOKEN',
'TELEGRAM_NOTIFIABLE_CHAT_ID','REVERB_APP_ID','REVERB_APP_KEY','REVERB_APP_SECRET','REVERB_HOST',
'REVERB_PORT','REVERB_SCHEME','VITE_REVERB_APP_KEY','VITE_REVERB_HOST','VITE_REVERB_PORT',
'VITE_REVERB_SCHEME','HLS_ENABLED',);
        
                expectedArguments(\Illuminate\Support\Facades\Gate::has(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::allows(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::denies(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::check(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::any(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::none(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::authorize(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Gate::inspect(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Support\Facades\Route::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Route::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Route::cant(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Support\Facades\Auth::cant(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::can(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::cannot(), 0, argumentsSet('auth'));
    expectedArguments(\Illuminate\Foundation\Auth\Access\Authorizable::cant(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Contracts\Auth\Access\Authorizable::can(), 0, argumentsSet('auth'));
                expectedArguments(\Illuminate\Config\Repository::getMany(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::set(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::string(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::integer(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::boolean(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::float(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::array(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::prepend(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Config\Repository::push(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::getMany(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::set(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::string(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::integer(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::boolean(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::float(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::array(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::prepend(), 0, argumentsSet('configs'));
    expectedArguments(\Illuminate\Support\Facades\Config::push(), 0, argumentsSet('configs'));
                expectedArguments(\Illuminate\Support\Facades\Route::middleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Support\Facades\Route::withoutMiddleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Routing\Router::middleware(), 0, argumentsSet('middleware'));
    expectedArguments(\Illuminate\Routing\Router::withoutMiddleware(), 0, argumentsSet('middleware'));
                expectedArguments(\route(), 0, argumentsSet('routes'));
    expectedArguments(\to_route(), 0, argumentsSet('routes'));
    expectedArguments(\signedRoute(), 0, argumentsSet('routes'));
                expectedArguments(\Illuminate\Support\Facades\Redirect::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\Redirect::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\Redirect::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Support\Facades\URL::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\Redirector::temporarySignedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::route(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::signedRoute(), 0, argumentsSet('routes'));
    expectedArguments(\Illuminate\Routing\UrlGenerator::temporarySignedRoute(), 0, argumentsSet('routes'));
                expectedArguments(\view(), 0, argumentsSet('views'));
                expectedArguments(\Illuminate\Support\Facades\View::make(), 0, argumentsSet('views'));
    expectedArguments(\Illuminate\View\Factory::make(), 0, argumentsSet('views'));
                expectedArguments(\__(), 0, argumentsSet('translations'));
    expectedArguments(\trans(), 0, argumentsSet('translations'));
                expectedArguments(\Illuminate\Contracts\Translation\Translator::get(), 0, argumentsSet('translations'));
                expectedArguments(\env(), 0, argumentsSet('env'));
                expectedArguments(\Illuminate\Support\Env::get(), 0, argumentsSet('env'));
            
}
