<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CompetitionResource\Pages;
use App\Filament\Resources\CompetitionResource\RelationManagers;
use App\Filament\Resources\RelationManagers\ImageRelationManager;
use App\Models\Competition;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CompetitionResource extends Resource
{
    protected static ?string $model = Competition::class;

    protected static ?string $navigationIcon = 'heroicon-o-trophy';

    protected static ?string $navigationGroup = 'Competitions';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Competition Details')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                        Forms\Components\DateTimePicker::make('start_date')
                            ->native(false)
                            ->placeholder('YYYY-MM-DD HH:MM')
                            ->displayFormat('Y-m-d H:i')
                            ->withoutSeconds()
                            ->required(),
                        Forms\Components\DateTimePicker::make('end_date')
                            ->native(false)
                            ->placeholder('YYYY-MM-DD HH:MM')
                            ->displayFormat('Y-m-d H:i')
                            ->withoutSeconds()
                            ->required(),
                        Forms\Components\Toggle::make('status')
                            ->label('Active')
                            ->required(),
                        Forms\Components\Select::make('type')
                            ->options([
                                'all' => 'All Artists',
                                'refugees_only' => 'On The Move Talents Awards (Refugees Only)',
                            ])
                            ->required(),
                        Forms\Components\Select::make('stage')
                            ->options([
                                1 => 'Stage 1',
                                2 => 'Stage 2',
                                3 => 'Stage 3',
                                4 => 'Stage 4',
                                5 => 'Stage 5 (Platinum)',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('vote_price')
                            ->label('Vote Price (USD)')
                            ->numeric()
                            ->default(1.00)
                            ->minValue(0.01)
                            ->maxValue(100.00)
                            ->step(0.01)
                            ->prefix('$')
                            ->required()
                            ->helperText('Price per vote in USD'),
                        Forms\Components\Toggle::make('auto_approve')
                            ->label('Auto-approve Entries')
                            ->helperText('Automatically approve entries that meet all requirements')
                            ->default(false),
                    ])->columns(2),

                Forms\Components\Section::make('Requirements')
                    ->schema([
                        Forms\Components\TextInput::make('requirements.min_followers')
                            ->label('Minimum Followers')
                            ->numeric()
                            ->default(250),
                        Forms\Components\TextInput::make('requirements.min_monthly_listeners')
                            ->label('Minimum Monthly Listeners')
                            ->numeric()
                            ->default(750),
                        Forms\Components\TextInput::make('requirements.min_tracks')
                            ->label('Minimum Published Tracks')
                            ->numeric()
                            ->default(3),
                    ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->formatStateUsing(fn (string $state): string => $state === 'refugees_only' ? 'OTM Awards' : 'All Artists'),
                Tables\Columns\TextColumn::make('stage')
                    ->badge()
                    ->formatStateUsing(fn (int $state): string => "Stage $state"),
                Tables\Columns\TextColumn::make('vote_price')
                    ->label('Vote Price')
                    ->money('USD')
                    ->sortable(),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\IconColumn::make('auto_approve')
                    ->label('Auto-approve')
                    ->boolean(),
                Tables\Columns\TextColumn::make('start_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_date')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ImageRelationManager::class,
            RelationManagers\EntriesRelationManager::class,
            RelationManagers\VotesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCompetitions::route('/'),
            'create' => Pages\CreateCompetition::route('/create'),
            'edit' => Pages\EditCompetition::route('/{record}/edit'),
            'view' => Pages\ViewCompetition::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
