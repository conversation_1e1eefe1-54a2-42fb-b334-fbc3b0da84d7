import { useIPage, useZiggy } from "@/hooks";
import { IoMusicalNotes, IoHeadset, IoRadio } from "react-icons/io5";

type FeatureCardProps = {
    icon: React.ReactNode;
    title: string;
    description: string;
};

function FeatureCard({ icon, title, description }: FeatureCardProps) {
    return (
        <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
            <div className="text-primary text-4xl mb-4">{icon}</div>
            <h3 className="text-xl font-bold mb-2">{title}</h3>
            <p className="text-gray-600">{description}</p>
        </div>
    );
}

const features = [
    {
        icon: <IoMusicalNotes className="animate-pulse" />,
        title: "Unlimited Music",
        description: "Access millions of songs from artists around the world.",
    },
    {
        icon: <IoHeadset />,
        title: "Premium Sound",
        description:
            "Experience high-quality audio with our advanced sound technology.",
    },
    {
        icon: <IoRadio />,
        title: "Personalized Playlists",
        description: "Discover new music tailored to your unique taste.",
    },
    // {
    //     icon: <IoPlayCircle />,
    //     title: "Offline Listening",
    //     description:
    //         "Download your favorite tracks and listen anywhere, anytime.",
    // },
];

export function FeaturesSection() {
    const { props } = useIPage();
    const route = useZiggy();

    return (
        <div className="w-full h-full flex flex-col relative bg-gradient-to-b from-white to-primary-50">
            <div className="w-full max-w-7xl mx-auto px-4 py-16">
                <div className="text-center mb-12">
                    <h2 className="text-3xl md:text-4xl font-bold mb-4">
                        Why Choose {props.appName}?
                    </h2>

                    <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                        Millions of individuals worldwide are forcibly displaced
                        due to conflict, persecution, and environmental crises.
                        Among them are talented artists whose voices often go
                        unheard due to limited access to platforms, networks,
                        and opportunities. Despite these challenges, music and
                        creative expression remain powerful tools for
                        storytelling, healing, and community building.
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {features.map((feature, index) => (
                        <FeatureCard
                            key={index}
                            icon={feature.icon}
                            title={feature.title}
                            description={feature.description}
                        />
                    ))}
                </div>

                {/* 
                
                SMOVEE.APP is a civic engagement project that aims to provide a digital space for refugee, internally displaced (IDP), and migrant artists to showcase and promote their stories, sounds, and music. This initiative will offer displaced individuals a platform to share their talents, preserve their creative heritage, and gain financial and social support through a structured community of practice.*/}

                <p className="text-lg text-gray-600 max-w-3xl mx-auto mt-12">
                    SMOVEE.APP is a civic engagement project that aims to
                    provide a digital space for refugee, internally displaced
                    (IDP), and migrant artists to showcase and promote their
                    stories, sounds, and music. This initiative will offer
                    displaced individuals a platform to share their talents,
                    preserve their creative heritage, and gain financial and
                    social support through a structured community of practice.
                </p>

                <div className="text-center mt-12">
                    <a
                        href={route("app.discover")}
                        className="bg-primary hover:bg-primary-600 text-white font-bold py-3 px-8 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                    >
                        Start Listening Now
                    </a>
                </div>
            </div>
        </div>
    );
}
