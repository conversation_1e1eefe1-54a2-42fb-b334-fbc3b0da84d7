import { ReactStoreBadges, useSwiperActiveSlide } from "@/components/lib";
import { NamedLogo } from "@/components/lib/ui";
import { useZiggy } from "@/hooks";
import { cn } from "@/utils";
import { IoArrowForward } from "react-icons/io5";

export function WelcomeSection() {
    const route = useZiggy();

    const { active, controller } = useSwiperActiveSlide(true);

    return (
        <div className="w-full h-full flex flex-row relative overflow-hidden">
            {/* Background decorative elements */}
            <div className="absolute inset-0 -z-10 overflow-hidden">
                <div className="absolute top-1/4 -left-10 w-72 h-72 bg-primary-100 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                <div className="absolute top-2/3 -right-10 w-72 h-72 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-primary-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>
            </div>

            {/* Web player button */}
            <a
                href={route("app.discover")}
                className="absolute right-7 top-7 px-4 py-2.5 text-white bg-primary hover:bg-primary-600 transition-colors duration-300 rounded-lg text-sm font-medium shadow-md"
            >
                Web player
            </a>

            <div className="w-full">
                <div className="flex items-center flex-col justify-center h-full px-4">
                    {/* Logo */}
                    <h1
                        className={cn(
                            "font-[700] text-[3rem] tablet:text-[5rem] overflow-hidden anim-wrapper mb-2",
                            active && ["active"]
                        )}
                    >
                        <NamedLogo logoClassName="w-16 h-16 tablet:w-24 tablet:h-24 fill-primary" />
                    </h1>

                    {/* Tagline */}
                    <h2 className="text-xl tablet:text-2xl font-medium text-center max-w-lg mb-3">
                        Stories, Sounds & Music on the Move
                    </h2>

                    {/* Description */}
                    <p className="text-center text-gray-600 max-w-md mb-8">
                        Displacement does not silence creativity. It amplifies
                        resilience.
                    </p>

                    {/* Learn more button */}
                    <div className="flex flex-col sm:flex-row gap-4">
                        <a
                            href={route("app.discover")}
                            className="bg-primary hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 group"
                        >
                            Try it now
                            <IoArrowForward className="group-hover:translate-x-1 transition-transform" />
                        </a>

                        <a
                            href="#features"
                            onClick={(e) => {
                                e.preventDefault();
                                controller?.current?.jumpTo(1);
                            }}
                            className="border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-3 px-6 rounded-full transition-all duration-300 flex items-center justify-center"
                        >
                            Learn More
                        </a>
                    </div>

                    {/* App store badges (hidden for now) */}
                    <div className="space-x-6 justify-center mt-2 hidden">
                        <div
                            className="w-12 h-12 transition-transform hover:scale-105"
                            title="Download on the App Store"
                        >
                            <ReactStoreBadges
                                platform="ios"
                                url="https://apps.apple.com"
                            />
                        </div>

                        <div
                            className="w-12 h-12 transition-transform hover:scale-105"
                            title="Get it on Google Play"
                        >
                            <ReactStoreBadges
                                platform="android"
                                url="https://play.google.com/store/apps"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
