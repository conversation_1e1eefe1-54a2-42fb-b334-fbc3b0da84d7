export function BkeLogo({ className = "fill-black" }) {
    return (
        <svg
            version="1.0"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="360 2 590.000000 900.000000"
            preserveAspectRatio="xMidYMid meet"
            className={className}
        >
            <g
                transform="translate(0.000000,1100.000000) scale(0.100000,-0.100000)"
                stroke="none"
            >
                <path
                    d="M4229 10813 l-516 -3 -4 -98 c-2 -53 -2 -468 1 -922 3 -454 7 -804 8
-777 1 26 7 47 12 47 6 0 10 -54 10 -142 0 -148 -11 -177 -21 -55 -3 40 -7
-124 -8 -363 -1 -239 -4 -822 -7 -1295 -6 -867 2 -1737 16 -1700 16 41 20 -14
24 -345 3 -184 9 -380 14 -435 21 -211 24 -231 32 -250 5 -11 14 -50 20 -88 7
-37 16 -69 20 -72 5 -3 11 -24 15 -48 11 -77 29 -142 42 -158 7 -8 13 -25 13
-37 0 -12 5 -34 11 -49 6 -15 15 -37 19 -48 16 -40 54 -122 106 -232 30 -61
54 -114 54 -116 0 -3 20 -35 45 -71 25 -37 45 -68 45 -71 0 -2 15 -25 33 -50
17 -26 37 -56 43 -67 6 -10 23 -31 36 -46 14 -15 36 -43 49 -63 47 -70 311
-349 331 -349 4 0 12 -8 17 -18 5 -10 44 -45 85 -77 42 -33 76 -63 76 -67 0
-5 6 -8 14 -8 8 0 16 -7 20 -15 3 -8 12 -15 21 -15 8 0 15 -9 15 -20 0 -15 7
-20 25 -20 18 0 23 -4 19 -15 -5 -11 1 -15 20 -15 16 0 26 -6 26 -15 0 -8 3
-14 8 -14 24 4 42 -3 42 -16 0 -8 4 -15 9 -15 6 0 21 -10 34 -23 14 -12 60
-40 103 -61 44 -21 82 -44 86 -51 5 -7 32 -19 60 -25 29 -7 64 -21 77 -31 18
-15 33 -17 64 -12 27 4 38 3 34 -4 -4 -6 5 -14 20 -18 17 -4 23 -9 15 -14 -7
-4 9 -13 39 -20 29 -8 77 -24 109 -36 53 -21 147 -44 325 -81 72 -15 268 -33
435 -41 107 -5 431 26 550 53 198 43 387 100 520 156 372 155 683 363 944 632
94 97 246 285 246 305 0 7 11 23 23 35 32 28 77 90 77 104 0 7 -5 12 -11 12
-6 0 -9 -6 -6 -14 3 -8 -2 -16 -12 -19 -17 -4 -17 -2 -4 22 8 14 23 40 33 56
11 17 23 37 27 45 3 8 16 29 29 45 12 17 25 40 29 53 3 12 10 22 13 22 4 0 16
22 27 50 16 41 23 49 40 45 11 -3 14 -2 8 1 -17 7 -16 14 4 60 9 22 16 42 15
47 -1 4 2 7 6 7 5 0 15 19 22 43 7 23 15 44 19 47 4 3 14 28 22 55 9 28 21 49
27 47 7 -1 12 11 12 29 0 42 28 136 46 154 9 8 13 18 11 23 -9 14 11 156 22
160 6 2 11 10 11 18 0 8 4 14 9 14 10 0 18 26 26 95 4 36 3 46 -4 35 -9 -13
-11 -13 -11 2 0 9 4 18 9 20 14 5 36 258 35 398 -4 407 -99 799 -284 1175
-354 715 -993 1240 -1775 1458 -254 71 -414 91 -730 91 -263 0 -372 -10 -565
-50 -95 -20 -311 -80 -356 -99 l-34 -14 0 -600 c0 -331 2 -601 5 -601 2 0 30
17 62 37 32 20 103 58 158 84 562 263 1219 207 1717 -149 172 -123 363 -325
468 -497 379 -618 334 -1386 -115 -1960 -35 -45 -106 -122 -158 -171 -479
-453 -1152 -594 -1771 -370 -51 19 -111 43 -134 55 -23 12 -44 21 -48 21 -15
0 44 -38 83 -53 70 -27 117 -56 119 -72 3 -20 -39 -20 -59 -1 -8 8 -31 18 -51
21 -20 4 -36 11 -36 15 0 4 -20 13 -45 20 -25 7 -45 17 -45 21 0 5 -8 9 -18 9
-10 0 -54 25 -98 55 -43 30 -85 55 -91 55 -7 0 -13 4 -13 9 0 5 -13 14 -30 21
-16 7 -30 16 -30 20 0 5 -29 32 -65 61 -95 77 -312 325 -349 399 -9 19 -28 53
-41 75 -41 70 -55 98 -55 108 0 12 -40 97 -55 117 -7 8 -20 54 -30 103 -10 48
-22 90 -26 93 -5 3 -6 16 -3 30 3 13 1 30 -5 38 -6 7 -11 27 -11 42 0 16 -4
92 -9 169 -6 102 -6 145 3 160 8 15 12 606 14 2265 l2 2246 -42 -4 c-53 -6
-102 19 -122 63 -31 65 -15 122 44 164 19 13 27 21 17 17 -10 -3 -42 -9 -70
-12 -64 -7 -111 19 -133 73 -29 69 5 142 79 167 l39 13 -32 6 c-42 9 -85 54
-94 99 -14 77 40 140 131 154 l43 6 -43 22 c-79 39 -97 128 -41 195 21 25 40
33 124 52 l100 22 0 158 c0 134 -2 158 -16 163 -9 3 -16 5 -17 4 -1 -2 -234
-4 -518 -5z m-489 -2190 c0 -48 -3 -64 -10 -53 -16 24 -11 120 6 120 2 0 4
-30 4 -67z m4376 -4328 c-4 -20 -14 -39 -22 -42 -7 -3 -14 -15 -14 -28 0 -29
-19 -86 -36 -106 -8 -8 -19 -31 -26 -50 -6 -19 -19 -45 -28 -59 -32 -46 -55
-83 -66 -102 -11 -18 -99 -119 -163 -186 -18 -18 -36 -30 -41 -27 -14 8 -12
21 3 37 7 7 40 49 74 93 33 44 70 89 82 99 12 11 21 25 21 33 0 7 5 13 10 13
6 0 10 5 10 12 0 6 12 27 28 46 30 40 45 66 117 210 28 55 52 99 54 97 2 -2 0
-20 -3 -40z"
                />
                <path
                    d="M4700 10459 c-99 -23 -122 -36 -140 -78 -18 -44 -5 -95 31 -125 38
-32 64 -32 199 -2 88 20 108 29 127 53 34 42 32 106 -6 144 -36 35 -82 37
-211 8z"
                />
                <path
                    d="M4736 10209 c-99 -22 -190 -46 -201 -53 -31 -20 -49 -69 -41 -110 8
-43 56 -86 97 -86 33 0 341 64 379 78 33 13 66 50 74 84 10 43 -17 96 -58 113
-46 19 -41 20 -250 -26z"
                />
                <path
                    d="M4899 9986 c-2 -2 -80 -20 -174 -40 -93 -20 -179 -43 -190 -50 -55
-36 -56 -126 -4 -170 40 -34 62 -33 254 9 196 43 189 41 225 72 26 21 30 32
30 71 0 50 -18 81 -57 101 -21 11 -75 15 -84 7z"
                />
                <path
                    d="M4860 9715 c-30 -7 -82 -18 -114 -25 -81 -17 -116 -51 -116 -115 0
-38 5 -52 29 -76 41 -41 70 -39 279 13 88 22 122 116 65 177 -35 37 -72 43
-143 26z"
                />
                <path
                    d="M5890 5640 c0 -212 2 -251 14 -247 24 10 48 -16 44 -48 -2 -24 -8
-31 -30 -33 l-27 -3 0 -180 0 -179 115 0 c62 0 114 -2 114 -6 0 -3 -14 -19
-30 -37 -17 -18 -43 -57 -58 -87 -23 -47 -27 -68 -27 -140 0 -75 4 -93 32
-152 17 -37 46 -88 63 -113 l31 -45 -120 0 -120 0 -3 -147 c-2 -82 -2 -150 -1
-152 2 -3 1001 532 1468 786 44 24 108 58 143 76 34 17 61 35 59 40 -2 7 -513
288 -1407 774 -107 58 -210 114 -227 125 l-33 20 0 -252z m185 -155 c0 -23
-34 -33 -45 -14 -13 20 1 40 24 37 13 -2 21 -10 21 -23z m-95 -10 c20 -25 8
-50 -25 -50 -21 0 -31 5 -33 18 -4 21 14 47 33 47 7 0 18 -7 25 -15z m226 -52
c9 -33 -16 -58 -49 -49 -36 9 -46 45 -18 67 27 22 59 13 67 -18z m-180 -24
c10 -17 -13 -36 -27 -22 -12 12 -4 33 11 33 5 0 12 -5 16 -11z m262 -23 c3
-13 -1 -17 -14 -14 -11 2 -20 11 -22 22 -3 13 1 17 14 14 11 -2 20 -11 22 -22z
m-221 -42 c8 -21 -13 -42 -28 -27 -13 13 -5 43 11 43 6 0 13 -7 17 -16z m87 0
c21 -8 21 -43 0 -51 -8 -3 -23 1 -31 10 -13 13 -14 19 -3 31 14 18 13 18 34
10z m170 -46 c14 -23 -3 -52 -33 -56 -42 -6 -67 38 -39 66 17 17 58 11 72 -10z
m93 -52 c-4 -9 -16 -16 -27 -16 -29 0 -38 30 -15 47 16 11 21 11 33 -1 8 -8
12 -22 9 -30z m-299 -30 c12 -23 11 -31 -3 -52 -20 -31 -37 -38 -70 -30 -35 9
-52 48 -37 81 23 50 86 51 110 1z m-175 19 c1 -5 -6 -11 -15 -13 -11 -2 -18 3
-18 13 0 17 30 18 33 0z m503 -71 c13 -53 -56 -77 -78 -28 -11 23 -10 29 10
44 28 23 60 15 68 -16z m-196 11 c27 -32 -23 -82 -55 -55 -15 13 -20 51 -8 63
12 12 50 7 63 -8z m68 -22 c-4 -22 -22 -20 -26 1 -2 10 3 16 13 16 10 0 15 -7
13 -17z m180 -71 c4 -28 -24 -40 -45 -19 -14 13 -14 19 -3 32 18 22 44 15 48
-13z m-233 -32 c0 -22 -29 -18 -33 3 -3 14 1 18 15 15 10 -2 18 -10 18 -18z"
                />
                <path d="M3713 5360 c0 -25 2 -35 4 -22 2 12 2 32 0 45 -2 12 -4 2 -4 -23z" />
                <path d="M4796 4627 c3 -10 9 -15 12 -12 3 3 0 11 -7 18 -10 9 -11 8 -5 -6z" />
                <path d="M8921 3614 c0 -11 3 -14 6 -6 3 7 2 16 -1 19 -3 4 -6 -2 -5 -13z" />
            </g>
        </svg>
    );
}

export function NamedLogo({ className = "", logoClassName = "" }) {
    return (
        <div className="flex items-center logo-anime">
            <span className={className}>Sm</span>
            <BkeLogo className={logoClassName} />
            <span className={className}>vee</span>
        </div>
    );
}
