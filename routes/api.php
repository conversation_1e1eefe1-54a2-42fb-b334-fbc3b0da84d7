<?php

use App\Features\DumpItemsApi;
use App\Http\Controllers\API\IssueAuthTokenController;
use App\Http\Controllers\AppWeb;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use Laravel\Pennant\Middleware\EnsureFeaturesAreActive;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

// Issue Auth Token
Route::prefix('auth')
    ->group(function () {
        Route::post('token', IssueAuthTokenController::class);
    });

Route::middleware('auth:sanctum')
    ->group(function () {
        Route::get('/user', function (Request $request) {
            return $request->user();
        });

        Route::get('/discover', AppWeb\DiscoverController::class);

        // Podcasts
        Route::get('/podcasts', AppWeb\PodcastsController::class);

        // Favorites

        Route::get('/favorites', AppWeb\FavoritesController::class);

        // Search

        Route::prefix('search')
            ->group(function () {
                Route::get('/', AppWeb\SearchController::class);

                Route::get('g/{genre}', [AppWeb\GenresController::class, 'show']);

                Route::get('g/{genre}/articles', [AppWeb\GenresController::class, 'genreArticles']);
            });

        // Genres

        Route::prefix('genres')
            ->group(function () {
                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::get('/random', [AppWeb\GenresController::class, 'random']);
                    });
            });

        // Artists

        Route::prefix('artists')
            ->group(function () {
                Route::get('/', AppWeb\ArtistesController::class);

                Route::get('/{user}', [AppWeb\ArtistesController::class, 'show']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [AppWeb\ArtistesController::class, 'create']);

                        Route::post('/update/image ', [AppWeb\ArtistesController::class, 'updateImage']);

                        Route::get('/without/image', [AppWeb\ArtistesController::class, 'getArtistsWithoutImage']);

                        Route::get('/search/name', [AppWeb\ArtistesController::class, 'getByName']);

                        Route::get('/search/email', [AppWeb\ArtistesController::class, 'getByEmail']);

                        Route::get('{user}/search/channel/name', [AppWeb\ArtistesController::class, 'getArtistChannelByName']);
                    });
            });

        // Articles

        Route::prefix('articles')
            ->name('articles.')
            ->group(function () {
                Route::get('/{article}/j/view', [AppWeb\ArticlesController::class, 'view']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [AppWeb\ArticlesController::class, 'create']);
                    });
            });

        // Channels

        Route::prefix('channels')
            ->group(function () {
                Route::get('/', AppWeb\ChannelsController::class);

                Route::get('/{channel}/j/articles', [AppWeb\ChannelsController::class, 'articles']);

                Route::get('/{channel}', [AppWeb\ChannelsController::class, 'show']);

                Route::get('/{channel}/download', [AppWeb\ChannelsController::class, 'download']);

                Route::get('/{channel}/j/view', [AppWeb\ChannelsController::class, 'view']);

                Route::middleware(EnsureFeaturesAreActive::using(DumpItemsApi::class))
                    ->withoutMiddleware('throttle:api')
                    ->group(function () {
                        Route::post('/', [AppWeb\ChannelsController::class, 'create']);

                        Route::post('/update/image ', [AppWeb\ChannelsController::class, 'updateImage']);

                        Route::get('/without/image', [AppWeb\ChannelsController::class, 'getChannelsWithoutImage']);

                        Route::get('{channel}/search/article/name', [AppWeb\ChannelsController::class, 'getChannelArticleByName']);
                    });
            });

        // Plays
        Route::put('plays/article/{article}', [AppWeb\PlaysController::class, 'storeArticlePlay']);

        // Following
        Route::put('followings/toggle/{user}', [AppWeb\FollowingsController::class, 'toggle']);

        // Like

        Route::prefix('like')
            ->group(function () {
                Route::put('/channel/{channel}', [AppWeb\LikeController::class, 'channel']);

                Route::put('/article/{article}', [AppWeb\LikeController::class, 'article']);
            });

        // Notifications

        Route::prefix('notifications')
            ->group(function () {
                Route::post('/clear', [AppWeb\NotificationsController::class, 'clear']);

                Route::delete('/{notificationId}/destroy', [AppWeb\NotificationsController::class, 'destroy']);
            });

        // Comments

        Route::prefix('comments')
            ->group(function () {
                Route::put('{comment}/update', [AppWeb\CommentsController::class, 'update']);

                Route::delete('{comment}/delete', [AppWeb\CommentsController::class, 'delete']);

                Route::prefix('{article}/article')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'article']);

                        Route::post('/create', [AppWeb\CommentsController::class, 'articleCommentStore']);
                    });

                Route::prefix('{channel}/channel')
                    ->group(function () {
                        Route::get('/', [AppWeb\CommentsController::class, 'channel']);

                        Route::post('/create', [AppWeb\CommentsController::class, 'ChannelCommentStore']);
                    });
            });

        // Settings

        Route::prefix('s')
            ->group(function () {
                Route::get('/account', AppWeb\Settings\AccountController::class);

                // Update auth user image profile
                Route::post('/account/image-profile', [AppWeb\Settings\AccountController::class, 'updateImageProfile']);

                Route::post('/artist-request', [AppWeb\Settings\ArtistRequestController::class, 'store']);
            });
    });
